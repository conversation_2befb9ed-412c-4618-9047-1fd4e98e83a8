{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "b6b161862b2f4331c9fd909608a789b2", "previewModeSigningKey": "d7329acc489fdaf3cbead66b3c25a314a776d79a76e5df00b144964ab955cdb7", "previewModeEncryptionKey": "4660c7ddd2084b021350cca412af1226a84865d4e269e3b97df860b1a9b66396"}}