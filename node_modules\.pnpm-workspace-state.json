{"lastValidatedTimestamp": 1752911095957, "projects": {"C:\\Users\\<USER>\\Desktop\\encreasl": {"name": "encreasl", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\encreasl\\apps\\web": {"name": "@encreasl/web", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\encreasl\\packages\\eslint-config": {"name": "@encreasl/eslint-config", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\encreasl\\packages\\typescript-config": {"name": "@encreasl/typescript-config", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\encreasl\\packages\\ui": {"name": "@encreasl/ui", "version": "0.1.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "hoisted", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "packages/*"]}, "filteredInstall": false}