hoistPattern:
  - '*'
hoistedLocations:
  '@alloc/quick-lru@5.2.0':
    - node_modules\@alloc\quick-lru
  '@ampproject/remapping@2.3.0':
    - node_modules\@ampproject\remapping
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0(jiti@2.4.2))':
    - node_modules\@eslint-community\eslint-utils
  '@eslint-community/regexpp@4.12.1':
    - node_modules\@eslint-community\regexpp
  '@eslint/config-array@0.21.0':
    - node_modules\@eslint\config-array
  '@eslint/config-helpers@0.3.0':
    - node_modules\@eslint\config-helpers
  '@eslint/core@0.15.1':
    - node_modules\@eslint\core
  '@eslint/eslintrc@3.3.1':
    - node_modules\@eslint\eslintrc
  '@eslint/js@9.31.0':
    - node_modules\@eslint\js
  '@eslint/object-schema@2.1.6':
    - node_modules\@eslint\object-schema
  '@eslint/plugin-kit@0.3.3':
    - node_modules\@eslint\plugin-kit
  '@humanfs/core@0.19.1':
    - node_modules\@humanfs\core
  '@humanfs/node@0.16.6':
    - node_modules\@humanfs\node
  '@humanwhocodes/module-importer@1.0.1':
    - node_modules\@humanwhocodes\module-importer
  '@humanwhocodes/retry@0.3.1':
    - node_modules\@humanwhocodes\retry
  '@humanwhocodes/retry@0.4.3':
    - node_modules\eslint\node_modules\@humanwhocodes\retry
  '@img/sharp-win32-x64@0.34.3':
    - node_modules\@img\sharp-win32-x64
  '@isaacs/fs-minipass@4.0.1':
    - node_modules\@isaacs\fs-minipass
  '@jridgewell/gen-mapping@0.3.12':
    - node_modules\@jridgewell\gen-mapping
  '@jridgewell/resolve-uri@3.1.2':
    - node_modules\@jridgewell\resolve-uri
  '@jridgewell/sourcemap-codec@1.5.4':
    - node_modules\@jridgewell\sourcemap-codec
  '@jridgewell/trace-mapping@0.3.29':
    - node_modules\@jridgewell\trace-mapping
  '@next/env@15.4.2':
    - node_modules\@next\env
  '@next/eslint-plugin-next@15.4.2':
    - node_modules\@next\eslint-plugin-next
  '@next/swc-win32-x64-msvc@15.4.2':
    - node_modules\@next\swc-win32-x64-msvc
  '@nodelib/fs.scandir@2.1.5':
    - node_modules\@nodelib\fs.scandir
  '@nodelib/fs.stat@2.0.5':
    - node_modules\@nodelib\fs.stat
  '@nodelib/fs.walk@1.2.8':
    - node_modules\@nodelib\fs.walk
  '@nolyfill/is-core-module@1.0.39':
    - node_modules\@nolyfill\is-core-module
  '@rtsao/scc@1.1.0':
    - node_modules\@rtsao\scc
  '@rushstack/eslint-patch@1.12.0':
    - node_modules\@rushstack\eslint-patch
  '@swc/helpers@0.5.15':
    - node_modules\@swc\helpers
  '@tailwindcss/node@4.1.11':
    - node_modules\@tailwindcss\node
  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    - node_modules\@tailwindcss\oxide-win32-x64-msvc
  '@tailwindcss/oxide@4.1.11':
    - node_modules\@tailwindcss\oxide
  '@tailwindcss/postcss@4.1.11':
    - node_modules\@tailwindcss\postcss
  '@types/estree@1.0.8':
    - node_modules\@types\estree
  '@types/json-schema@7.0.15':
    - node_modules\@types\json-schema
  '@types/json5@0.0.29':
    - node_modules\@types\json5
  '@types/node@20.19.9':
    - node_modules\@types\node
  '@types/react-dom@19.1.6(@types/react@19.1.8)':
    - node_modules\@types\react-dom
  '@types/react@19.1.8':
    - node_modules\@types\react
  '@typescript-eslint/eslint-plugin@8.37.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\eslint-plugin
  '@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\parser
  '@typescript-eslint/project-service@8.37.0(typescript@5.8.3)':
    - node_modules\@typescript-eslint\project-service
  '@typescript-eslint/scope-manager@8.37.0':
    - node_modules\@typescript-eslint\scope-manager
  '@typescript-eslint/tsconfig-utils@8.37.0(typescript@5.8.3)':
    - node_modules\@typescript-eslint\tsconfig-utils
  '@typescript-eslint/type-utils@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\type-utils
  '@typescript-eslint/types@8.37.0':
    - node_modules\@typescript-eslint\types
  '@typescript-eslint/typescript-estree@8.37.0(typescript@5.8.3)':
    - node_modules\@typescript-eslint\typescript-estree
  '@typescript-eslint/utils@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3)':
    - node_modules\@typescript-eslint\utils
  '@typescript-eslint/visitor-keys@8.37.0':
    - node_modules\@typescript-eslint\visitor-keys
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    - node_modules\@unrs\resolver-binding-win32-x64-msvc
  acorn-jsx@5.3.2(acorn@8.15.0):
    - node_modules\acorn-jsx
  acorn@8.15.0:
    - node_modules\acorn
  ajv@6.12.6:
    - node_modules\ajv
  ansi-styles@4.3.0:
    - node_modules\ansi-styles
  argparse@2.0.1:
    - node_modules\argparse
  aria-query@5.3.2:
    - node_modules\aria-query
  array-buffer-byte-length@1.0.2:
    - node_modules\array-buffer-byte-length
  array-includes@3.1.9:
    - node_modules\array-includes
  array.prototype.findlast@1.2.5:
    - node_modules\array.prototype.findlast
  array.prototype.findlastindex@1.2.6:
    - node_modules\array.prototype.findlastindex
  array.prototype.flat@1.3.3:
    - node_modules\array.prototype.flat
  array.prototype.flatmap@1.3.3:
    - node_modules\array.prototype.flatmap
  array.prototype.tosorted@1.1.4:
    - node_modules\array.prototype.tosorted
  arraybuffer.prototype.slice@1.0.4:
    - node_modules\arraybuffer.prototype.slice
  ast-types-flow@0.0.8:
    - node_modules\ast-types-flow
  async-function@1.0.0:
    - node_modules\async-function
  available-typed-arrays@1.0.7:
    - node_modules\available-typed-arrays
  axe-core@4.10.3:
    - node_modules\axe-core
  axobject-query@4.1.0:
    - node_modules\axobject-query
  balanced-match@1.0.2:
    - node_modules\balanced-match
  brace-expansion@1.1.12:
    - node_modules\minimatch\node_modules\brace-expansion
  brace-expansion@2.0.2:
    - node_modules\brace-expansion
  braces@3.0.3:
    - node_modules\braces
  call-bind-apply-helpers@1.0.2:
    - node_modules\call-bind-apply-helpers
  call-bind@1.0.8:
    - node_modules\call-bind
  call-bound@1.0.4:
    - node_modules\call-bound
  callsites@3.1.0:
    - node_modules\callsites
  caniuse-lite@1.0.30001727:
    - node_modules\caniuse-lite
  chalk@4.1.2:
    - node_modules\chalk
  chownr@3.0.0:
    - node_modules\chownr
  client-only@0.0.1:
    - node_modules\client-only
  color-convert@2.0.1:
    - node_modules\color-convert
  color-name@1.1.4:
    - node_modules\color-name
  color-string@1.9.1:
    - node_modules\color-string
  color@4.2.3:
    - node_modules\color
  concat-map@0.0.1:
    - node_modules\concat-map
  cross-spawn@7.0.6:
    - node_modules\cross-spawn
  csstype@3.1.3:
    - node_modules\csstype
  damerau-levenshtein@1.0.8:
    - node_modules\damerau-levenshtein
  data-view-buffer@1.0.2:
    - node_modules\data-view-buffer
  data-view-byte-length@1.0.2:
    - node_modules\data-view-byte-length
  data-view-byte-offset@1.0.1:
    - node_modules\data-view-byte-offset
  debug@3.2.7:
    - node_modules\eslint-import-resolver-node\node_modules\debug
    - node_modules\eslint-plugin-import\node_modules\debug
    - node_modules\eslint-module-utils\node_modules\debug
  debug@4.4.1:
    - node_modules\debug
  deep-is@0.1.4:
    - node_modules\deep-is
  define-data-property@1.1.4:
    - node_modules\define-data-property
  define-properties@1.2.1:
    - node_modules\define-properties
  detect-libc@2.0.4:
    - node_modules\detect-libc
  doctrine@2.1.0:
    - node_modules\doctrine
  dunder-proto@1.0.1:
    - node_modules\dunder-proto
  emoji-regex@9.2.2:
    - node_modules\emoji-regex
  enhanced-resolve@5.18.2:
    - node_modules\enhanced-resolve
  es-abstract@1.24.0:
    - node_modules\es-abstract
  es-define-property@1.0.1:
    - node_modules\es-define-property
  es-errors@1.3.0:
    - node_modules\es-errors
  es-iterator-helpers@1.2.1:
    - node_modules\es-iterator-helpers
  es-object-atoms@1.1.1:
    - node_modules\es-object-atoms
  es-set-tostringtag@2.1.0:
    - node_modules\es-set-tostringtag
  es-shim-unscopables@1.1.0:
    - node_modules\es-shim-unscopables
  es-to-primitive@1.3.0:
    - node_modules\es-to-primitive
  escape-string-regexp@4.0.0:
    - node_modules\escape-string-regexp
  eslint-config-next@15.4.2(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3):
    - node_modules\eslint-config-next
  eslint-import-resolver-node@0.3.9:
    - node_modules\eslint-import-resolver-node
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0)(eslint@9.31.0(jiti@2.4.2)):
    - node_modules\eslint-import-resolver-typescript
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@9.31.0(jiti@2.4.2)):
    - node_modules\eslint-module-utils
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@8.37.0(eslint@9.31.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.1)(eslint@9.31.0(jiti@2.4.2)):
    - node_modules\eslint-plugin-import
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.31.0(jiti@2.4.2)):
    - node_modules\eslint-plugin-jsx-a11y
  eslint-plugin-react-hooks@5.2.0(eslint@9.31.0(jiti@2.4.2)):
    - node_modules\eslint-plugin-react-hooks
  eslint-plugin-react@7.37.5(eslint@9.31.0(jiti@2.4.2)):
    - node_modules\eslint-plugin-react
  eslint-scope@8.4.0:
    - node_modules\eslint-scope
  eslint-visitor-keys@3.4.3:
    - node_modules\@eslint-community\eslint-utils\node_modules\eslint-visitor-keys
  eslint-visitor-keys@4.2.1:
    - node_modules\eslint-visitor-keys
  eslint@9.31.0(jiti@2.4.2):
    - node_modules\eslint
  espree@10.4.0:
    - node_modules\espree
  esquery@1.6.0:
    - node_modules\esquery
  esrecurse@4.3.0:
    - node_modules\esrecurse
  estraverse@5.3.0:
    - node_modules\estraverse
  esutils@2.0.3:
    - node_modules\esutils
  fast-deep-equal@3.1.3:
    - node_modules\fast-deep-equal
  fast-glob@3.3.1:
    - node_modules\@next\eslint-plugin-next\node_modules\fast-glob
  fast-glob@3.3.3:
    - node_modules\fast-glob
  fast-json-stable-stringify@2.1.0:
    - node_modules\fast-json-stable-stringify
  fast-levenshtein@2.0.6:
    - node_modules\fast-levenshtein
  fastq@1.19.1:
    - node_modules\fastq
  fdir@6.4.6(picomatch@4.0.3):
    - node_modules\fdir
  file-entry-cache@8.0.0:
    - node_modules\file-entry-cache
  fill-range@7.1.1:
    - node_modules\fill-range
  find-up@5.0.0:
    - node_modules\find-up
  flat-cache@4.0.1:
    - node_modules\flat-cache
  flatted@3.3.3:
    - node_modules\flatted
  for-each@0.3.5:
    - node_modules\for-each
  function-bind@1.1.2:
    - node_modules\function-bind
  function.prototype.name@1.1.8:
    - node_modules\function.prototype.name
  functions-have-names@1.2.3:
    - node_modules\functions-have-names
  get-intrinsic@1.3.0:
    - node_modules\get-intrinsic
  get-proto@1.0.1:
    - node_modules\get-proto
  get-symbol-description@1.1.0:
    - node_modules\get-symbol-description
  get-tsconfig@4.10.1:
    - node_modules\get-tsconfig
  glob-parent@5.1.2:
    - node_modules\glob-parent
  glob-parent@6.0.2:
    - node_modules\eslint\node_modules\glob-parent
  globals@14.0.0:
    - node_modules\globals
  globalthis@1.0.4:
    - node_modules\globalthis
  gopd@1.2.0:
    - node_modules\gopd
  graceful-fs@4.2.11:
    - node_modules\graceful-fs
  graphemer@1.4.0:
    - node_modules\graphemer
  has-bigints@1.1.0:
    - node_modules\has-bigints
  has-flag@4.0.0:
    - node_modules\has-flag
  has-property-descriptors@1.0.2:
    - node_modules\has-property-descriptors
  has-proto@1.2.0:
    - node_modules\has-proto
  has-symbols@1.1.0:
    - node_modules\has-symbols
  has-tostringtag@1.0.2:
    - node_modules\has-tostringtag
  hasown@2.0.2:
    - node_modules\hasown
  ignore@5.3.2:
    - node_modules\ignore
  ignore@7.0.5:
    - node_modules\@typescript-eslint\eslint-plugin\node_modules\ignore
  import-fresh@3.3.1:
    - node_modules\import-fresh
  imurmurhash@0.1.4:
    - node_modules\imurmurhash
  internal-slot@1.1.0:
    - node_modules\internal-slot
  is-array-buffer@3.0.5:
    - node_modules\is-array-buffer
  is-arrayish@0.3.2:
    - node_modules\is-arrayish
  is-async-function@2.1.1:
    - node_modules\is-async-function
  is-bigint@1.1.0:
    - node_modules\is-bigint
  is-boolean-object@1.2.2:
    - node_modules\is-boolean-object
  is-bun-module@2.0.0:
    - node_modules\is-bun-module
  is-callable@1.2.7:
    - node_modules\is-callable
  is-core-module@2.16.1:
    - node_modules\is-core-module
  is-data-view@1.0.2:
    - node_modules\is-data-view
  is-date-object@1.1.0:
    - node_modules\is-date-object
  is-extglob@2.1.1:
    - node_modules\is-extglob
  is-finalizationregistry@1.1.1:
    - node_modules\is-finalizationregistry
  is-generator-function@1.1.0:
    - node_modules\is-generator-function
  is-glob@4.0.3:
    - node_modules\is-glob
  is-map@2.0.3:
    - node_modules\is-map
  is-negative-zero@2.0.3:
    - node_modules\is-negative-zero
  is-number-object@1.1.1:
    - node_modules\is-number-object
  is-number@7.0.0:
    - node_modules\is-number
  is-regex@1.2.1:
    - node_modules\is-regex
  is-set@2.0.3:
    - node_modules\is-set
  is-shared-array-buffer@1.0.4:
    - node_modules\is-shared-array-buffer
  is-string@1.1.1:
    - node_modules\is-string
  is-symbol@1.1.1:
    - node_modules\is-symbol
  is-typed-array@1.1.15:
    - node_modules\is-typed-array
  is-weakmap@2.0.2:
    - node_modules\is-weakmap
  is-weakref@1.1.1:
    - node_modules\is-weakref
  is-weakset@2.0.4:
    - node_modules\is-weakset
  isarray@2.0.5:
    - node_modules\isarray
  isexe@2.0.0:
    - node_modules\isexe
  iterator.prototype@1.1.5:
    - node_modules\iterator.prototype
  jiti@2.4.2:
    - node_modules\jiti
  js-tokens@4.0.0:
    - node_modules\js-tokens
  js-yaml@4.1.0:
    - node_modules\js-yaml
  json-buffer@3.0.1:
    - node_modules\json-buffer
  json-schema-traverse@0.4.1:
    - node_modules\json-schema-traverse
  json-stable-stringify-without-jsonify@1.0.1:
    - node_modules\json-stable-stringify-without-jsonify
  json5@1.0.2:
    - node_modules\json5
  jsx-ast-utils@3.3.5:
    - node_modules\jsx-ast-utils
  keyv@4.5.4:
    - node_modules\keyv
  language-subtag-registry@0.3.23:
    - node_modules\language-subtag-registry
  language-tags@1.0.9:
    - node_modules\language-tags
  levn@0.4.1:
    - node_modules\levn
  lightningcss-win32-x64-msvc@1.30.1:
    - node_modules\lightningcss-win32-x64-msvc
  lightningcss@1.30.1:
    - node_modules\lightningcss
  locate-path@6.0.0:
    - node_modules\locate-path
  lodash.merge@4.6.2:
    - node_modules\lodash.merge
  loose-envify@1.4.0:
    - node_modules\loose-envify
  magic-string@0.30.17:
    - node_modules\magic-string
  math-intrinsics@1.1.0:
    - node_modules\math-intrinsics
  merge2@1.4.1:
    - node_modules\merge2
  micromatch@4.0.8:
    - node_modules\micromatch
  minimatch@3.1.2:
    - node_modules\minimatch
  minimatch@9.0.5:
    - node_modules\@typescript-eslint\typescript-estree\node_modules\minimatch
  minimist@1.2.8:
    - node_modules\minimist
  minipass@7.1.2:
    - node_modules\minipass
  minizlib@3.0.2:
    - node_modules\minizlib
  mkdirp@3.0.1:
    - node_modules\mkdirp
  ms@2.1.3:
    - node_modules\ms
  nanoid@3.3.11:
    - node_modules\nanoid
  napi-postinstall@0.3.2:
    - node_modules\napi-postinstall
  natural-compare@1.4.0:
    - node_modules\natural-compare
  next@15.4.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    - node_modules\next
  object-assign@4.1.1:
    - node_modules\object-assign
  object-inspect@1.13.4:
    - node_modules\object-inspect
  object-keys@1.1.1:
    - node_modules\object-keys
  object.assign@4.1.7:
    - node_modules\object.assign
  object.entries@1.1.9:
    - node_modules\object.entries
  object.fromentries@2.0.8:
    - node_modules\object.fromentries
  object.groupby@1.0.3:
    - node_modules\object.groupby
  object.values@1.2.1:
    - node_modules\object.values
  optionator@0.9.4:
    - node_modules\optionator
  own-keys@1.0.1:
    - node_modules\own-keys
  p-limit@3.1.0:
    - node_modules\p-limit
  p-locate@5.0.0:
    - node_modules\p-locate
  parent-module@1.0.1:
    - node_modules\parent-module
  path-exists@4.0.0:
    - node_modules\path-exists
  path-key@3.1.1:
    - node_modules\path-key
  path-parse@1.0.7:
    - node_modules\path-parse
  picocolors@1.1.1:
    - node_modules\picocolors
  picomatch@2.3.1:
    - node_modules\micromatch\node_modules\picomatch
  picomatch@4.0.3:
    - node_modules\picomatch
  possible-typed-array-names@1.1.0:
    - node_modules\possible-typed-array-names
  postcss@8.4.31:
    - node_modules\postcss
  postcss@8.5.6:
    - node_modules\@tailwindcss\postcss\node_modules\postcss
  prelude-ls@1.2.1:
    - node_modules\prelude-ls
  prop-types@15.8.1:
    - node_modules\prop-types
  punycode@2.3.1:
    - node_modules\punycode
  queue-microtask@1.2.3:
    - node_modules\queue-microtask
  react-dom@19.1.0(react@19.1.0):
    - node_modules\react-dom
  react-is@16.13.1:
    - node_modules\react-is
  react@19.1.0:
    - node_modules\react
  reflect.getprototypeof@1.0.10:
    - node_modules\reflect.getprototypeof
  regexp.prototype.flags@1.5.4:
    - node_modules\regexp.prototype.flags
  resolve-from@4.0.0:
    - node_modules\resolve-from
  resolve-pkg-maps@1.0.0:
    - node_modules\resolve-pkg-maps
  resolve@1.22.10:
    - node_modules\resolve
  resolve@2.0.0-next.5:
    - node_modules\eslint-plugin-react\node_modules\resolve
  reusify@1.1.0:
    - node_modules\reusify
  run-parallel@1.2.0:
    - node_modules\run-parallel
  safe-array-concat@1.1.3:
    - node_modules\safe-array-concat
  safe-push-apply@1.0.0:
    - node_modules\safe-push-apply
  safe-regex-test@1.1.0:
    - node_modules\safe-regex-test
  scheduler@0.26.0:
    - node_modules\scheduler
  semver@6.3.1:
    - node_modules\eslint-plugin-react\node_modules\semver
    - node_modules\eslint-plugin-import\node_modules\semver
  semver@7.7.2:
    - node_modules\semver
  set-function-length@1.2.2:
    - node_modules\set-function-length
  set-function-name@2.0.2:
    - node_modules\set-function-name
  set-proto@1.0.0:
    - node_modules\set-proto
  sharp@0.34.3:
    - node_modules\sharp
  shebang-command@2.0.0:
    - node_modules\shebang-command
  shebang-regex@3.0.0:
    - node_modules\shebang-regex
  side-channel-list@1.0.0:
    - node_modules\side-channel-list
  side-channel-map@1.0.1:
    - node_modules\side-channel-map
  side-channel-weakmap@1.0.2:
    - node_modules\side-channel-weakmap
  side-channel@1.1.0:
    - node_modules\side-channel
  simple-swizzle@0.2.2:
    - node_modules\simple-swizzle
  source-map-js@1.2.1:
    - node_modules\source-map-js
  stable-hash@0.0.5:
    - node_modules\stable-hash
  stop-iteration-iterator@1.1.0:
    - node_modules\stop-iteration-iterator
  string.prototype.includes@2.0.1:
    - node_modules\string.prototype.includes
  string.prototype.matchall@4.0.12:
    - node_modules\string.prototype.matchall
  string.prototype.repeat@1.0.0:
    - node_modules\string.prototype.repeat
  string.prototype.trim@1.2.10:
    - node_modules\string.prototype.trim
  string.prototype.trimend@1.0.9:
    - node_modules\string.prototype.trimend
  string.prototype.trimstart@1.0.8:
    - node_modules\string.prototype.trimstart
  strip-bom@3.0.0:
    - node_modules\strip-bom
  strip-json-comments@3.1.1:
    - node_modules\strip-json-comments
  styled-jsx@5.1.6(react@19.1.0):
    - node_modules\styled-jsx
  supports-color@7.2.0:
    - node_modules\supports-color
  supports-preserve-symlinks-flag@1.0.0:
    - node_modules\supports-preserve-symlinks-flag
  tailwindcss@4.1.11:
    - node_modules\tailwindcss
  tapable@2.2.2:
    - node_modules\tapable
  tar@7.4.3:
    - node_modules\tar
  tinyglobby@0.2.14:
    - node_modules\tinyglobby
  to-regex-range@5.0.1:
    - node_modules\to-regex-range
  ts-api-utils@2.1.0(typescript@5.8.3):
    - node_modules\ts-api-utils
  tsconfig-paths@3.15.0:
    - node_modules\tsconfig-paths
  tslib@2.8.1:
    - node_modules\tslib
  turbo-windows-64@2.5.5:
    - node_modules\turbo-windows-64
  turbo@2.5.5:
    - node_modules\turbo
  type-check@0.4.0:
    - node_modules\type-check
  typed-array-buffer@1.0.3:
    - node_modules\typed-array-buffer
  typed-array-byte-length@1.0.3:
    - node_modules\typed-array-byte-length
  typed-array-byte-offset@1.0.4:
    - node_modules\typed-array-byte-offset
  typed-array-length@1.0.7:
    - node_modules\typed-array-length
  typescript@5.8.3:
    - node_modules\typescript
  unbox-primitive@1.1.0:
    - node_modules\unbox-primitive
  undici-types@6.21.0:
    - node_modules\undici-types
  unrs-resolver@1.11.1:
    - node_modules\unrs-resolver
  uri-js@4.4.1:
    - node_modules\uri-js
  which-boxed-primitive@1.1.1:
    - node_modules\which-boxed-primitive
  which-builtin-type@1.2.1:
    - node_modules\which-builtin-type
  which-collection@1.0.2:
    - node_modules\which-collection
  which-typed-array@1.1.19:
    - node_modules\which-typed-array
  which@2.0.2:
    - node_modules\which
  word-wrap@1.2.5:
    - node_modules\word-wrap
  yallist@5.0.0:
    - node_modules\yallist
  yocto-queue@0.1.0:
    - node_modules\yocto-queue
ignoredBuilds:
  - sharp
  - unrs-resolver
  - '@tailwindcss/oxide'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: hoisted
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Sat, 19 Jul 2025 07:06:29 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.5'
  - '@emnapi/runtime@1.4.5'
  - '@emnapi/wasi-threads@1.0.4'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-arm64@15.4.2'
  - '@next/swc-darwin-x64@15.4.2'
  - '@next/swc-linux-arm64-gnu@15.4.2'
  - '@next/swc-linux-arm64-musl@15.4.2'
  - '@next/swc-linux-x64-gnu@15.4.2'
  - '@next/swc-linux-x64-musl@15.4.2'
  - '@next/swc-win32-arm64-msvc@15.4.2'
  - '@tailwindcss/oxide-android-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-arm64@4.1.11'
  - '@tailwindcss/oxide-darwin-x64@4.1.11'
  - '@tailwindcss/oxide-freebsd-x64@4.1.11'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.11'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.11'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.11'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.11'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.11'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - turbo-darwin-64@2.5.5
  - turbo-darwin-arm64@2.5.5
  - turbo-linux-64@2.5.5
  - turbo-linux-arm64@2.5.5
  - turbo-windows-arm64@2.5.5
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\encreasl\node_modules\.pnpm
virtualStoreDirMaxLength: 60
