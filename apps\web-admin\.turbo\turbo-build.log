[?9001h[?1004h[?25l[2J[m[H]0;C:\WINDOWS\system32\cmd.exe [?25h[?25l
> @encreasl/web-admin@0.1.0 build C:\Users\<USER>\Desktop\encreasl\apps\web-admin
> next build[5;1H[?25h   [38;2;173;127;168m[1m▲ Next.js 15.4.2
[m
 [37m[1m [m Creating an optimized production build ...
 [32m[1m✓[m Compiled successfully in 6.0s
[?25l [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
 ⚠ The Next.js plugin was not detected in your ESLint configuration. See https://nextjs.org/docs/app/api-reference/config/eslint#migrating-existing-config
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m...[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m.[m
[K[82C
 [37m[1m [m Linting and checking validity of types  [36m..[m
[K[82C
 [32m[1m✓[m Linting and checking validity of types    
 [37m[1m [m Collecting page data  [36m.[m
[K[82C
 [37m[1m [m Collecting page data  [36m..[m
[K[82C
 [37m[1m [m Collecting page data  [36m...[m
[K[82C
 [37m[1m [m Collecting page data  [36m.[m
[K[82C
 [37m[1m [m Collecting page data  [36m..[m
[K[82C
 [37m[1m [m Collecting page data  [36m...[m
[K[82C
 [37m[1m [m Collecting page data  [36m.[m
[K[82C
 [37m[1m [m Collecting page data  [36m..[m
[K[82C
 [32m[1m✓[m Collecting page data    
[?25h[?25l [37m[1m [m Generating static pages (0/4)  [36m[    ][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[=   ][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[==  ][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[=== ][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[ ===][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[  ==][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[   =][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[    ][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[   =][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[  ==][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[ ===][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[====][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[=== ][m
[K[82C
 [37m[1m [m Generating static pages (0/4)  [36m[==  ][m
[K[82C
 [37m[1m [m Generating static pages (1/4)  [36m[=   ][m
[K[82C
 [32m[1m✓[m Generating static pages (4/4)
[?25h[?25l [37m[1m [m Finalizing page optimization  [36m. [37m[1m [m Collecting build traces  [36m.[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m..[m
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m...[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m.[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m..[m
[K[82C
[K[82C
 [37m[1m [m Finalizing page optimization  [36m...[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m.[K[48C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m..[K[47C[m
[K[82C
[K[82C
 [37m[1m [m Finalizing page optimization  [36m...[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m.[K[48C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m..[K[47C[m
[K[82C
[K[82C
 [37m[1m [m Finalizing page optimization  [36m...[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m.[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m..[m
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m...[m
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m.[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[m
[K[82C
 [37m[1m [m Finalizing page optimization  [36m..[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m..[K[52C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m...[K[51C[m
[K[82C
 [37m[1m [m Collecting build traces  [36m.[K[53C[m
[K[82C
 [32m[1m✓[m Collecting build traces    
 [32m[1m✓[m Finalizing page optimization[K
[?25h
[4mRoute (app)[24m[33X[4m[33CSize[24m  [4mFirst Load JS[24m    
┌ ○ /                                      123 B[37m[1m[9C100 kB[m[K
└ ○ /_not-found                            987 B[37m[1m[9C101 kB[m[K
+ First Load JS shared by all[37m[1m[13C100 kB[m[K
  ├ chunks/87c73c54-1f4741035a95c140.js  54.1 kB
  ├ chunks/902-dab2a3ea1ce41df2.js       43.9 kB
  └ other shared chunks (total)          1.97 kB


○  (Static)  prerendered as static content

[?9001l[?1004l
