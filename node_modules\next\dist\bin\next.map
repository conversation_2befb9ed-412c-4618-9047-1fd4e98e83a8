{"version": 3, "sources": ["../../src/bin/next.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport '../server/require-hook'\n\nimport { Argument, Command, Option } from 'next/dist/compiled/commander'\n\nimport { warn } from '../build/output/log'\nimport semver from 'next/dist/compiled/semver'\nimport { bold, cyan, italic } from '../lib/picocolors'\nimport { formatCliHelpOutput } from '../lib/format-cli-help-output'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\nimport { parseValidPositiveInteger } from '../server/lib/utils'\nimport {\n  SUPPORTED_TEST_RUNNERS_LIST,\n  type NextTestOptions,\n} from '../cli/next-test.js'\nimport type { NextTelemetryOptions } from '../cli/next-telemetry.js'\nimport type { NextStartOptions } from '../cli/next-start.js'\nimport type { NextLintOptions } from '../cli/next-lint.js'\nimport type { NextInfoOptions } from '../cli/next-info.js'\nimport type { NextDevOptions } from '../cli/next-dev.js'\nimport type { NextBuildOptions } from '../cli/next-build.js'\n\nif (process.env.NEXT_RSPACK) {\n  // silent rspack's schema check\n  process.env.RSPACK_CONFIG_VALIDATE = 'loose-silent'\n}\n\nif (\n  !semver.satisfies(\n    process.versions.node,\n    process.env.__NEXT_REQUIRED_NODE_VERSION_RANGE!,\n    { includePrerelease: true }\n  )\n) {\n  console.error(\n    `You are using Node.js ${process.versions.node}. For Next.js, Node.js version \"${process.env.__NEXT_REQUIRED_NODE_VERSION_RANGE}\" is required.`\n  )\n  process.exit(1)\n}\n\n// Start performance profiling after Node.js version is checked\nperformance.mark('next-start')\n\nfor (const dependency of ['react', 'react-dom']) {\n  try {\n    // When 'npm link' is used it checks the clone location. Not the project.\n    require.resolve(dependency)\n  } catch (err) {\n    console.warn(\n      `The module '${dependency}' was not found. Next.js requires that you include it in 'dependencies' of your 'package.json'. To add it, run 'npm install ${dependency}'`\n    )\n  }\n}\n\nclass NextRootCommand extends Command {\n  createCommand(name: string) {\n    const command = new Command(name)\n\n    command.addOption(new Option('--inspect').hideHelp())\n\n    command.hook('preAction', (event) => {\n      const commandName = event.name()\n      const defaultEnv = commandName === 'dev' ? 'development' : 'production'\n      const standardEnv = ['production', 'development', 'test']\n\n      if (process.env.NODE_ENV) {\n        const isNotStandard = !standardEnv.includes(process.env.NODE_ENV)\n        const shouldWarnCommands =\n          process.env.NODE_ENV === 'development'\n            ? ['start', 'build']\n            : process.env.NODE_ENV === 'production'\n              ? ['dev']\n              : []\n\n        if (isNotStandard || shouldWarnCommands.includes(commandName)) {\n          warn(NON_STANDARD_NODE_ENV)\n        }\n      }\n\n      ;(process.env as any).NODE_ENV = process.env.NODE_ENV || defaultEnv\n      ;(process.env as any).NEXT_RUNTIME = 'nodejs'\n\n      if (event.getOptionValue('inspect') === true) {\n        console.error(\n          `\\`--inspect\\` flag is deprecated. Use env variable NODE_OPTIONS instead: NODE_OPTIONS='--inspect' next ${commandName}`\n        )\n        process.exit(1)\n      }\n    })\n\n    return command\n  }\n}\n\nconst program = new NextRootCommand()\n\nprogram\n  .name('next')\n  .description(\n    'The Next.js CLI allows you to develop, build, start your application, and more.'\n  )\n  .configureHelp({\n    formatHelp: (cmd, helper) => formatCliHelpOutput(cmd, helper),\n    subcommandTerm: (cmd) => `${cmd.name()} ${cmd.usage()}`,\n  })\n  .helpCommand(false)\n  .helpOption('-h, --help', 'Displays this message.')\n  .version(\n    `Next.js v${process.env.__NEXT_VERSION}`,\n    '-v, --version',\n    'Outputs the Next.js version.'\n  )\n\nprogram\n  .command('build')\n  .description(\n    'Creates an optimized production build of your application. The output displays information about each route.'\n  )\n  .argument(\n    '[directory]',\n    `A directory on which to build the application. ${italic(\n      'If no directory is provided, the current directory will be used.'\n    )}`\n  )\n  .option('-d, --debug', 'Enables a more verbose build output.')\n  .option(\n    '--debug-prerender',\n    'Enables debug mode for prerendering. Not for production use!'\n  )\n  .option('--no-lint', 'Disables linting.')\n  .option('--no-mangling', 'Disables mangling.')\n  .option('--profile', 'Enables production profiling for React.')\n  .option('--experimental-app-only', 'Builds only App Router routes.')\n  .option('--turbo', 'Starts development mode using Turbopack.')\n  .option('--turbopack', 'Starts development mode using Turbopack.')\n  .addOption(\n    new Option(\n      '--experimental-build-mode [mode]',\n      'Uses an experimental build mode.'\n    )\n      .choices(['compile', 'generate', 'generate-env'])\n      .default('default')\n  )\n  .option(\n    '--experimental-debug-memory-usage',\n    'Enables memory profiling features to debug memory consumption.'\n  )\n  .option(\n    '--experimental-upload-trace, <traceUrl>',\n    'Reports a subset of the debugging trace to a remote HTTP URL. Includes sensitive data.'\n  )\n  .action((directory: string, options: NextBuildOptions) =>\n    // ensure process exits after build completes so open handles/connections\n    // don't cause process to hang\n    import('../cli/next-build.js').then((mod) =>\n      mod.nextBuild(options, directory).then(() => process.exit(0))\n    )\n  )\n  .usage('[directory] [options]')\n\nprogram\n  .command('dev', { isDefault: true })\n  .description(\n    'Starts Next.js in development mode with hot-code reloading, error reporting, and more.'\n  )\n  .argument(\n    '[directory]',\n    `A directory on which to build the application. ${italic(\n      'If no directory is provided, the current directory will be used.'\n    )}`\n  )\n  .option('--turbo', 'Starts development mode using Turbopack.')\n  .option('--turbopack', 'Starts development mode using Turbopack.')\n  .addOption(\n    new Option(\n      '-p, --port <port>',\n      'Specify a port number on which to start the application.'\n    )\n      .argParser(parseValidPositiveInteger)\n      .default(3000)\n      .env('PORT')\n  )\n  .option(\n    '-H, --hostname <hostname>',\n    'Specify a hostname on which to start the application (default: 0.0.0.0).'\n  )\n  .option(\n    '--disable-source-maps',\n    \"Don't start the Dev server with `--enable-source-maps`.\",\n    false\n  )\n  .option(\n    '--experimental-https',\n    'Starts the server with HTTPS and generates a self-signed certificate.'\n  )\n  .option('--experimental-https-key, <path>', 'Path to a HTTPS key file.')\n  .option(\n    '--experimental-https-cert, <path>',\n    'Path to a HTTPS certificate file.'\n  )\n  .option(\n    '--experimental-https-ca, <path>',\n    'Path to a HTTPS certificate authority file.'\n  )\n  .option(\n    '--experimental-upload-trace, <traceUrl>',\n    'Reports a subset of the debugging trace to a remote HTTP URL. Includes sensitive data.'\n  )\n  .action(\n    (directory: string, options: NextDevOptions, { _optionValueSources }) => {\n      const portSource = _optionValueSources.port\n      import('../cli/next-dev.js').then((mod) =>\n        mod.nextDev(options, portSource, directory)\n      )\n    }\n  )\n  .usage('[directory] [options]')\n\nprogram\n  .command('export', { hidden: true })\n  .action(() => import('../cli/next-export.js').then((mod) => mod.nextExport()))\n  .helpOption(false)\n\nprogram\n  .command('info')\n  .description(\n    'Prints relevant details about the current system which can be used to report Next.js bugs.'\n  )\n  .addHelpText(\n    'after',\n    `\\nLearn more: ${cyan('https://nextjs.org/docs/api-reference/cli#info')}`\n  )\n  .option('--verbose', 'Collects additional information for debugging.')\n  .action((options: NextInfoOptions) =>\n    import('../cli/next-info.js').then((mod) => mod.nextInfo(options))\n  )\n\nprogram\n  .command('lint')\n  .description(\n    'Runs ESLint for all files in the `/src`, `/app`, `/pages`, `/components`, and `/lib` directories. It also provides a guided setup to install any required dependencies if ESLint is not already configured in your application.'\n  )\n  .argument(\n    '[directory]',\n    `A base directory on which to lint the application. ${italic(\n      'If no directory is provided, the current directory will be used.'\n    )}`\n  )\n  .option(\n    '-d, --dir, <dirs...>',\n    'Include directory, or directories, to run ESLint.'\n  )\n  .option('--file, <files...>', 'Include file, or files, to run ESLint.')\n  .addOption(\n    new Option(\n      '--ext, [exts...]',\n      'Specify JavaScript file extensions.'\n    ).default(['.js', '.mjs', '.cjs', '.jsx', '.ts', '.mts', '.cts', '.tsx'])\n  )\n  .option(\n    '-c, --config, <config>',\n    'Uses this configuration file, overriding all other configuration options.'\n  )\n  .option(\n    '--resolve-plugins-relative-to, <rprt>',\n    'Specify a directory where plugins should be resolved from.'\n  )\n  .option(\n    '--strict',\n    'Creates a `.eslintrc.json` file using the Next.js strict configuration.'\n  )\n  .option(\n    '--rulesdir, <rulesdir...>',\n    'Uses additional rules from this directory(s).'\n  )\n  .option('--fix', 'Automatically fix linting issues.')\n  .option(\n    '--fix-type <fixType>',\n    'Specify the types of fixes to apply (e.g., problem, suggestion, layout).'\n  )\n  .option('--ignore-path <path>', 'Specify a file to ignore.')\n  .option('--no-ignore', 'Disables the `--ignore-path` option.')\n  .option('--quiet', 'Reports errors only.')\n  .addOption(\n    new Option(\n      '--max-warnings [maxWarnings]',\n      'Specify the number of warnings before triggering a non-zero exit code.'\n    )\n      .argParser(parseValidPositiveInteger)\n      .default(-1)\n  )\n  .option(\n    '-o, --output-file, <outputFile>',\n    'Specify a file to write report to.'\n  )\n  .option('-f, --format, <format>', 'Uses a specific output format.')\n  .option(\n    '--no-inline-config',\n    'Prevents comments from changing config or rules.'\n  )\n  .addOption(\n    new Option(\n      '--report-unused-disable-directives-severity <level>',\n      'Specify severity level for unused eslint-disable directives.'\n    ).choices(['error', 'off', 'warn'])\n  )\n  .option('--no-cache', 'Disables caching.')\n  .option('--cache-location, <cacheLocation>', 'Specify a location for cache.')\n  .addOption(\n    new Option(\n      '--cache-strategy, [cacheStrategy]',\n      'Specify a strategy to use for detecting changed files in the cache.'\n    ).default('metadata')\n  )\n  .option(\n    '--error-on-unmatched-pattern',\n    'Reports errors when any file patterns are unmatched.'\n  )\n  .action((directory: string, options: NextLintOptions) =>\n    import('../cli/next-lint.js').then((mod) =>\n      mod.nextLint(options, directory)\n    )\n  )\n  .usage('[directory] [options]')\n\nprogram\n  .command('start')\n  .description(\n    'Starts Next.js in production mode. The application should be compiled with `next build` first.'\n  )\n  .argument(\n    '[directory]',\n    `A directory on which to start the application. ${italic(\n      'If no directory is provided, the current directory will be used.'\n    )}`\n  )\n  .addOption(\n    new Option(\n      '-p, --port <port>',\n      'Specify a port number on which to start the application.'\n    )\n      .argParser(parseValidPositiveInteger)\n      .default(3000)\n      .env('PORT')\n  )\n  .option(\n    '-H, --hostname <hostname>',\n    'Specify a hostname on which to start the application (default: 0.0.0.0).'\n  )\n  .addOption(\n    new Option(\n      '--keepAliveTimeout <keepAliveTimeout>',\n      'Specify the maximum amount of milliseconds to wait before closing inactive connections.'\n    ).argParser(parseValidPositiveInteger)\n  )\n  .action((directory: string, options: NextStartOptions) =>\n    import('../cli/next-start.js').then((mod) =>\n      mod.nextStart(options, directory)\n    )\n  )\n  .usage('[directory] [options]')\n\nprogram\n  .command('telemetry')\n  .description(\n    `Allows you to enable or disable Next.js' ${bold(\n      'completely anonymous'\n    )} telemetry collection.`\n  )\n  .addArgument(new Argument('[arg]').choices(['disable', 'enable', 'status']))\n  .addHelpText('after', `\\nLearn more: ${cyan('https://nextjs.org/telemetry')}`)\n  .addOption(\n    new Option('--enable', `Enables Next.js' telemetry collection.`).conflicts(\n      'disable'\n    )\n  )\n  .option('--disable', `Disables Next.js' telemetry collection.`)\n  .action((arg: string, options: NextTelemetryOptions) =>\n    import('../cli/next-telemetry.js').then((mod) =>\n      mod.nextTelemetry(options, arg)\n    )\n  )\n\nprogram\n  .command('experimental-test')\n  .description(\n    `Execute \\`next/experimental/testmode\\` tests using a specified test runner. The test runner defaults to 'playwright' if the \\`experimental.defaultTestRunner\\` configuration option or the \\`--test-runner\\` option are not set.`\n  )\n  .argument(\n    '[directory]',\n    `A Next.js project directory to execute the test runner on. ${italic(\n      'If no directory is provided, the current directory will be used.'\n    )}`\n  )\n  .argument(\n    '[test-runner-args...]',\n    'Any additional arguments or options to pass down to the test runner `test` command.'\n  )\n  .option(\n    '--test-runner [test-runner]',\n    `Any supported test runner. Options: ${bold(\n      SUPPORTED_TEST_RUNNERS_LIST.join(', ')\n    )}. ${italic(\n      \"If no test runner is provided, the Next.js config option `experimental.defaultTestRunner`, or 'playwright' will be used.\"\n    )}`\n  )\n  .allowUnknownOption()\n  .action(\n    (directory: string, testRunnerArgs: string[], options: NextTestOptions) => {\n      return import('../cli/next-test.js').then((mod) => {\n        mod.nextTest(directory, testRunnerArgs, options)\n      })\n    }\n  )\n  .usage('[directory] [options]')\n\nconst internal = program\n  .command('internal')\n  .description(\n    'Internal debugging commands. Use with caution. Not covered by semver.'\n  )\n\ninternal\n  .command('trace')\n  .alias('turbo-trace-server')\n  .argument('[file]', 'Trace file to serve.')\n  .action((file: string) => {\n    return import('../cli/internal/turbo-trace-server.js').then((mod) =>\n      mod.startTurboTraceServerCli(file)\n    )\n  })\n\nprogram.parse(process.argv)\n"], "names": ["process", "env", "NEXT_RSPACK", "RSPACK_CONFIG_VALIDATE", "semver", "satisfies", "versions", "node", "__NEXT_REQUIRED_NODE_VERSION_RANGE", "includePrerelease", "console", "error", "exit", "performance", "mark", "dependency", "require", "resolve", "err", "warn", "NextRootCommand", "Command", "createCommand", "name", "command", "addOption", "Option", "hideHelp", "hook", "event", "commandName", "defaultEnv", "standardEnv", "NODE_ENV", "isNotStandard", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NON_STANDARD_NODE_ENV", "NEXT_RUNTIME", "getOptionValue", "program", "description", "configureHelp", "formatHelp", "cmd", "helper", "formatCliHelpOutput", "subcommandTerm", "usage", "helpCommand", "helpOption", "version", "__NEXT_VERSION", "argument", "italic", "option", "choices", "default", "action", "directory", "options", "then", "mod", "nextBuild", "isDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseValidPositiveInteger", "_optionValueSources", "portSource", "port", "nextDev", "hidden", "nextExport", "addHelpText", "cyan", "nextInfo", "nextLint", "nextStart", "bold", "addArgument", "Argument", "conflicts", "arg", "nextTelemetry", "SUPPORTED_TEST_RUNNERS_LIST", "join", "allowUnknownOption", "testRunnerArgs", "nextTest", "internal", "alias", "file", "startTurboTraceServerCli", "parse", "argv"], "mappings": ";;;;;QAEO;2BAEmC;qBAErB;+DACF;4BACgB;qCACC;2BACE;uBACI;0BAInC;;;;;;AAQP,IAAIA,QAAQC,GAAG,CAACC,WAAW,EAAE;IAC3B,+BAA+B;IAC/BF,QAAQC,GAAG,CAACE,sBAAsB,GAAG;AACvC;AAEA,IACE,CAACC,eAAM,CAACC,SAAS,CACfL,QAAQM,QAAQ,CAACC,IAAI,EACrBP,QAAQC,GAAG,CAACO,kCAAkC,EAC9C;IAAEC,mBAAmB;AAAK,IAE5B;IACAC,QAAQC,KAAK,CACX,CAAC,sBAAsB,EAAEX,QAAQM,QAAQ,CAACC,IAAI,CAAC,gCAAgC,EAAEP,QAAQC,GAAG,CAACO,kCAAkC,CAAC,cAAc,CAAC;IAEjJR,QAAQY,IAAI,CAAC;AACf;AAEA,+DAA+D;AAC/DC,YAAYC,IAAI,CAAC;AAEjB,KAAK,MAAMC,cAAc;IAAC;IAAS;CAAY,CAAE;IAC/C,IAAI;QACF,yEAAyE;QACzEC,QAAQC,OAAO,CAACF;IAClB,EAAE,OAAOG,KAAK;QACZR,QAAQS,IAAI,CACV,CAAC,YAAY,EAAEJ,WAAW,4HAA4H,EAAEA,WAAW,CAAC,CAAC;IAEzK;AACF;AAEA,MAAMK,wBAAwBC,kBAAO;IACnCC,cAAcC,IAAY,EAAE;QAC1B,MAAMC,UAAU,IAAIH,kBAAO,CAACE;QAE5BC,QAAQC,SAAS,CAAC,IAAIC,iBAAM,CAAC,aAAaC,QAAQ;QAElDH,QAAQI,IAAI,CAAC,aAAa,CAACC;YACzB,MAAMC,cAAcD,MAAMN,IAAI;YAC9B,MAAMQ,aAAaD,gBAAgB,QAAQ,gBAAgB;YAC3D,MAAME,cAAc;gBAAC;gBAAc;gBAAe;aAAO;YAEzD,IAAIhC,QAAQC,GAAG,CAACgC,QAAQ,EAAE;gBACxB,MAAMC,gBAAgB,CAACF,YAAYG,QAAQ,CAACnC,QAAQC,GAAG,CAACgC,QAAQ;gBAChE,MAAMG,qBACJpC,QAAQC,GAAG,CAACgC,QAAQ,KAAK,gBACrB;oBAAC;oBAAS;iBAAQ,GAClBjC,QAAQC,GAAG,CAACgC,QAAQ,KAAK,eACvB;oBAAC;iBAAM,GACP,EAAE;gBAEV,IAAIC,iBAAiBE,mBAAmBD,QAAQ,CAACL,cAAc;oBAC7DX,IAAAA,SAAI,EAACkB,gCAAqB;gBAC5B;YACF;;YAEErC,QAAQC,GAAG,CAASgC,QAAQ,GAAGjC,QAAQC,GAAG,CAACgC,QAAQ,IAAIF;YACvD/B,QAAQC,GAAG,CAASqC,YAAY,GAAG;YAErC,IAAIT,MAAMU,cAAc,CAAC,eAAe,MAAM;gBAC5C7B,QAAQC,KAAK,CACX,CAAC,uGAAuG,EAAEmB,aAAa;gBAEzH9B,QAAQY,IAAI,CAAC;YACf;QACF;QAEA,OAAOY;IACT;AACF;AAEA,MAAMgB,UAAU,IAAIpB;AAEpBoB,QACGjB,IAAI,CAAC,QACLkB,WAAW,CACV,mFAEDC,aAAa,CAAC;IACbC,YAAY,CAACC,KAAKC,SAAWC,IAAAA,wCAAmB,EAACF,KAAKC;IACtDE,gBAAgB,CAACH,MAAQ,GAAGA,IAAIrB,IAAI,GAAG,CAAC,EAAEqB,IAAII,KAAK,IAAI;AACzD,GACCC,WAAW,CAAC,OACZC,UAAU,CAAC,cAAc,0BACzBC,OAAO,CACN,CAAC,SAAS,EAAEnD,QAAQC,GAAG,CAACmD,cAAc,EAAE,EACxC,iBACA;AAGJZ,QACGhB,OAAO,CAAC,SACRiB,WAAW,CACV,gHAEDY,QAAQ,CACP,eACA,CAAC,+CAA+C,EAAEC,IAAAA,kBAAM,EACtD,qEACC,EAEJC,MAAM,CAAC,eAAe,wCACtBA,MAAM,CACL,qBACA,gEAEDA,MAAM,CAAC,aAAa,qBACpBA,MAAM,CAAC,iBAAiB,sBACxBA,MAAM,CAAC,aAAa,2CACpBA,MAAM,CAAC,2BAA2B,kCAClCA,MAAM,CAAC,WAAW,4CAClBA,MAAM,CAAC,eAAe,4CACtB9B,SAAS,CACR,IAAIC,iBAAM,CACR,oCACA,oCAEC8B,OAAO,CAAC;IAAC;IAAW;IAAY;CAAe,EAC/CC,OAAO,CAAC,YAEZF,MAAM,CACL,qCACA,kEAEDA,MAAM,CACL,2CACA,0FAEDG,MAAM,CAAC,CAACC,WAAmBC,UAC1B,yEAAyE;IACzE,8BAA8B;IAC9B,MAAM,CAAC,wBAAwBC,IAAI,CAAC,CAACC,MACnCA,IAAIC,SAAS,CAACH,SAASD,WAAWE,IAAI,CAAC,IAAM7D,QAAQY,IAAI,CAAC,MAG7DoC,KAAK,CAAC;AAETR,QACGhB,OAAO,CAAC,OAAO;IAAEwC,WAAW;AAAK,GACjCvB,WAAW,CACV,0FAEDY,QAAQ,CACP,eACA,CAAC,+CAA+C,EAAEC,IAAAA,kBAAM,EACtD,qEACC,EAEJC,MAAM,CAAC,WAAW,4CAClBA,MAAM,CAAC,eAAe,4CACtB9B,SAAS,CACR,IAAIC,iBAAM,CACR,qBACA,4DAECuC,SAAS,CAACC,gCAAyB,EACnCT,OAAO,CAAC,MACRxD,GAAG,CAAC,SAERsD,MAAM,CACL,6BACA,4EAEDA,MAAM,CACL,yBACA,2DACA,OAEDA,MAAM,CACL,wBACA,yEAEDA,MAAM,CAAC,oCAAoC,6BAC3CA,MAAM,CACL,qCACA,qCAEDA,MAAM,CACL,mCACA,+CAEDA,MAAM,CACL,2CACA,0FAEDG,MAAM,CACL,CAACC,WAAmBC,SAAyB,EAAEO,mBAAmB,EAAE;IAClE,MAAMC,aAAaD,oBAAoBE,IAAI;IAC3C,MAAM,CAAC,sBAAsBR,IAAI,CAAC,CAACC,MACjCA,IAAIQ,OAAO,CAACV,SAASQ,YAAYT;AAErC,GAEDX,KAAK,CAAC;AAETR,QACGhB,OAAO,CAAC,UAAU;IAAE+C,QAAQ;AAAK,GACjCb,MAAM,CAAC,IAAM,MAAM,CAAC,yBAAyBG,IAAI,CAAC,CAACC,MAAQA,IAAIU,UAAU,KACzEtB,UAAU,CAAC;AAEdV,QACGhB,OAAO,CAAC,QACRiB,WAAW,CACV,8FAEDgC,WAAW,CACV,SACA,CAAC,cAAc,EAAEC,IAAAA,gBAAI,EAAC,mDAAmD,EAE1EnB,MAAM,CAAC,aAAa,kDACpBG,MAAM,CAAC,CAACE,UACP,MAAM,CAAC,uBAAuBC,IAAI,CAAC,CAACC,MAAQA,IAAIa,QAAQ,CAACf;AAG7DpB,QACGhB,OAAO,CAAC,QACRiB,WAAW,CACV,mOAEDY,QAAQ,CACP,eACA,CAAC,mDAAmD,EAAEC,IAAAA,kBAAM,EAC1D,qEACC,EAEJC,MAAM,CACL,wBACA,qDAEDA,MAAM,CAAC,sBAAsB,0CAC7B9B,SAAS,CACR,IAAIC,iBAAM,CACR,oBACA,uCACA+B,OAAO,CAAC;IAAC;IAAO;IAAQ;IAAQ;IAAQ;IAAO;IAAQ;IAAQ;CAAO,GAEzEF,MAAM,CACL,0BACA,6EAEDA,MAAM,CACL,yCACA,8DAEDA,MAAM,CACL,YACA,2EAEDA,MAAM,CACL,6BACA,iDAEDA,MAAM,CAAC,SAAS,qCAChBA,MAAM,CACL,wBACA,4EAEDA,MAAM,CAAC,wBAAwB,6BAC/BA,MAAM,CAAC,eAAe,wCACtBA,MAAM,CAAC,WAAW,wBAClB9B,SAAS,CACR,IAAIC,iBAAM,CACR,gCACA,0EAECuC,SAAS,CAACC,gCAAyB,EACnCT,OAAO,CAAC,CAAC,IAEbF,MAAM,CACL,mCACA,sCAEDA,MAAM,CAAC,0BAA0B,kCACjCA,MAAM,CACL,sBACA,oDAED9B,SAAS,CACR,IAAIC,iBAAM,CACR,uDACA,gEACA8B,OAAO,CAAC;IAAC;IAAS;IAAO;CAAO,GAEnCD,MAAM,CAAC,cAAc,qBACrBA,MAAM,CAAC,qCAAqC,iCAC5C9B,SAAS,CACR,IAAIC,iBAAM,CACR,qCACA,uEACA+B,OAAO,CAAC,aAEXF,MAAM,CACL,gCACA,wDAEDG,MAAM,CAAC,CAACC,WAAmBC,UAC1B,MAAM,CAAC,uBAAuBC,IAAI,CAAC,CAACC,MAClCA,IAAIc,QAAQ,CAAChB,SAASD,aAGzBX,KAAK,CAAC;AAETR,QACGhB,OAAO,CAAC,SACRiB,WAAW,CACV,kGAEDY,QAAQ,CACP,eACA,CAAC,+CAA+C,EAAEC,IAAAA,kBAAM,EACtD,qEACC,EAEJ7B,SAAS,CACR,IAAIC,iBAAM,CACR,qBACA,4DAECuC,SAAS,CAACC,gCAAyB,EACnCT,OAAO,CAAC,MACRxD,GAAG,CAAC,SAERsD,MAAM,CACL,6BACA,4EAED9B,SAAS,CACR,IAAIC,iBAAM,CACR,yCACA,2FACAuC,SAAS,CAACC,gCAAyB,GAEtCR,MAAM,CAAC,CAACC,WAAmBC,UAC1B,MAAM,CAAC,wBAAwBC,IAAI,CAAC,CAACC,MACnCA,IAAIe,SAAS,CAACjB,SAASD,aAG1BX,KAAK,CAAC;AAETR,QACGhB,OAAO,CAAC,aACRiB,WAAW,CACV,CAAC,yCAAyC,EAAEqC,IAAAA,gBAAI,EAC9C,wBACA,sBAAsB,CAAC,EAE1BC,WAAW,CAAC,IAAIC,mBAAQ,CAAC,SAASxB,OAAO,CAAC;IAAC;IAAW;IAAU;CAAS,GACzEiB,WAAW,CAAC,SAAS,CAAC,cAAc,EAAEC,IAAAA,gBAAI,EAAC,iCAAiC,EAC5EjD,SAAS,CACR,IAAIC,iBAAM,CAAC,YAAY,CAAC,sCAAsC,CAAC,EAAEuD,SAAS,CACxE,YAGH1B,MAAM,CAAC,aAAa,CAAC,uCAAuC,CAAC,EAC7DG,MAAM,CAAC,CAACwB,KAAatB,UACpB,MAAM,CAAC,4BAA4BC,IAAI,CAAC,CAACC,MACvCA,IAAIqB,aAAa,CAACvB,SAASsB;AAIjC1C,QACGhB,OAAO,CAAC,qBACRiB,WAAW,CACV,CAAC,gOAAgO,CAAC,EAEnOY,QAAQ,CACP,eACA,CAAC,2DAA2D,EAAEC,IAAAA,kBAAM,EAClE,qEACC,EAEJD,QAAQ,CACP,yBACA,uFAEDE,MAAM,CACL,+BACA,CAAC,oCAAoC,EAAEuB,IAAAA,gBAAI,EACzCM,qCAA2B,CAACC,IAAI,CAAC,OACjC,EAAE,EAAE/B,IAAAA,kBAAM,EACV,6HACC,EAEJgC,kBAAkB,GAClB5B,MAAM,CACL,CAACC,WAAmB4B,gBAA0B3B;IAC5C,OAAO,MAAM,CAAC,uBAAuBC,IAAI,CAAC,CAACC;QACzCA,IAAI0B,QAAQ,CAAC7B,WAAW4B,gBAAgB3B;IAC1C;AACF,GAEDZ,KAAK,CAAC;AAET,MAAMyC,WAAWjD,QACdhB,OAAO,CAAC,YACRiB,WAAW,CACV;AAGJgD,SACGjE,OAAO,CAAC,SACRkE,KAAK,CAAC,sBACNrC,QAAQ,CAAC,UAAU,wBACnBK,MAAM,CAAC,CAACiC;IACP,OAAO,MAAM,CAAC,yCAAyC9B,IAAI,CAAC,CAACC,MAC3DA,IAAI8B,wBAAwB,CAACD;AAEjC;AAEFnD,QAAQqD,KAAK,CAAC7F,QAAQ8F,IAAI", "ignoreList": [0]}