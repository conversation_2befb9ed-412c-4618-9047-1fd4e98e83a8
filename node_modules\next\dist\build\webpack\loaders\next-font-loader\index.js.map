{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/index.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON>oa<PERSON> } from '../../../../../font'\n\nimport path from 'path'\nimport { bold, cyan } from '../../../../lib/picocolors'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport postcssNextFontPlugin from './postcss-next-font'\nimport { promisify } from 'util'\n\nexport default async function nextFontLoader(this: any) {\n  const nextFontLoaderSpan =\n    this.currentTraceSpan.traceChild('next-font-loader')\n  return nextFontLoaderSpan.traceAsyncFn(async () => {\n    const callback = this.async()\n\n    /**\n     * The next-swc plugin next-transform-font turns font function calls into CSS imports.\n     * At the end of the import, it adds the call arguments and some additional data as a resourceQuery.\n     * e.g:\n     * const inter = Inter({ subset: ['latin'] })\n     * ->\n     * import inter from 'next/font/google/target.css?{\"import\":\"Inter\",\"subsets\":[\"latin\"]}'\n     *\n     * Here we parse the resourceQuery to get the font function name, call arguments, and the path to the file that called the font function.\n     */\n    const {\n      path: relativeFilePathFromRoot,\n      import: functionName,\n      arguments: data,\n      variableName,\n    } = JSON.parse(this.resourceQuery.slice(1))\n\n    // Throw error if @next/font is used in _document.js\n    if (/pages[\\\\/]_document\\./.test(relativeFilePathFromRoot)) {\n      const err = new Error(\n        `${bold('Cannot')} be used within ${cyan('pages/_document.js')}.`\n      )\n      err.name = 'NextFontError'\n      callback(err)\n      return\n    }\n\n    const {\n      isDev,\n      isServer,\n      assetPrefix,\n      fontLoaderPath,\n      postcss: getPostcss,\n    } = this.getOptions()\n\n    if (assetPrefix && !/^\\/|https?:\\/\\//.test(assetPrefix)) {\n      const err = new Error(\n        'assetPrefix must start with a leading slash or be an absolute URL(http:// or https://)'\n      )\n      err.name = 'NextFontError'\n      callback(err)\n      return\n    }\n\n    /**\n     * Emit font files to .next/static/media as [hash].[ext].\n     *\n     * If the font should be preloaded, add .p to the filename: [hash].p.[ext]\n     * NextFontManifestPlugin adds these files to the next/font manifest.\n     *\n     * If the font is using a size-adjust fallback font, add -s to the filename: [hash]-s.[ext]\n     * NextFontManifestPlugin uses this to see if fallback fonts are being used.\n     * This is used to collect stats on fallback fonts usage by the Google Aurora team.\n     */\n    const emitFontFile = (\n      content: Buffer,\n      ext: string,\n      preload: boolean,\n      isUsingSizeAdjust?: boolean\n    ) => {\n      const opts = { context: this.rootContext, content }\n      const interpolatedName = loaderUtils.interpolateName(\n        this,\n        `static/media/[hash]${isUsingSizeAdjust ? '-s' : ''}${\n          preload ? '.p' : ''\n        }.${ext}`,\n        opts\n      )\n      const outputPath = `${assetPrefix}/_next/${interpolatedName}`\n      // Only the client emits the font file\n      if (!isServer) {\n        this.emitFile(interpolatedName, content, null)\n      }\n      // But both the server and client must get the resulting path\n      return outputPath\n    }\n\n    try {\n      // Import the font loader function from either next/font/local or next/font/google\n      // The font loader function emits font files and returns @font-faces and fallback font metrics\n      const fontLoader: FontLoader = require(fontLoaderPath).default\n      let { css, fallbackFonts, adjustFontFallback, weight, style, variable } =\n        await nextFontLoaderSpan.traceChild('font-loader').traceAsyncFn(() =>\n          fontLoader({\n            functionName,\n            variableName,\n            data,\n            emitFontFile,\n            resolve: (src: string) =>\n              promisify(this.resolve)(\n                path.dirname(\n                  path.join(this.rootContext, relativeFilePathFromRoot)\n                ),\n                src.startsWith('.') ? src : `./${src}`\n              ),\n            isDev,\n            isServer,\n            loaderContext: this,\n          })\n        )\n\n      const { postcss } = await getPostcss()\n\n      // Exports will be exported as is from css-loader instead of a CSS module export\n      const exports: { name: any; value: any }[] = []\n\n      // Generate a hash from the CSS content. Used to generate classnames\n      const fontFamilyHash = loaderUtils.getHashDigest(\n        Buffer.from(css),\n        'sha1',\n        'hex',\n        6\n      )\n\n      // Add CSS classes, exports and make the font-family locally scoped by turning it unguessable\n      const result = await nextFontLoaderSpan\n        .traceChild('postcss')\n        .traceAsyncFn(() =>\n          postcss(\n            postcssNextFontPlugin({\n              exports,\n              fallbackFonts,\n              weight,\n              style,\n              adjustFontFallback,\n              variable,\n            })\n          ).process(css, {\n            from: undefined,\n          })\n        )\n\n      const ast = {\n        type: 'postcss',\n        version: result.processor.version,\n        root: result.root,\n      }\n\n      // Return the resulting CSS and send the postcss ast, font exports and the hash to the css-loader in the meta argument.\n      callback(null, result.css, null, {\n        exports,\n        ast,\n        fontFamilyHash,\n      })\n    } catch (err: any) {\n      callback(err)\n    }\n  })\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextFontLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "callback", "async", "path", "relativeFilePathFromRoot", "import", "functionName", "arguments", "data", "variableName", "JSON", "parse", "resourceQuery", "slice", "test", "err", "Error", "bold", "cyan", "name", "isDev", "isServer", "assetPrefix", "fontLoaderPath", "postcss", "getPostcss", "getOptions", "emitFontFile", "content", "ext", "preload", "isUsingSizeAdjust", "opts", "context", "rootContext", "interpolatedName", "loaderUtils", "interpolateName", "outputPath", "emitFile", "fontLoader", "require", "default", "css", "fallbackFonts", "adjustFontFallback", "weight", "style", "variable", "resolve", "src", "promisify", "dirname", "join", "startsWith", "loaderContext", "exports", "fontFamilyHash", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "result", "postcssNextFontPlugin", "process", "undefined", "ast", "type", "version", "processor", "root"], "mappings": ";;;;+BAQA;;;eAA8BA;;;6DANb;4BACU;qEACH;wEACU;sBACR;;;;;;AAEX,eAAeA;IAC5B,MAAMC,qBACJ,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACnC,OAAOF,mBAAmBG,YAAY,CAAC;QACrC,MAAMC,WAAW,IAAI,CAACC,KAAK;QAE3B;;;;;;;;;KASC,GACD,MAAM,EACJC,MAAMC,wBAAwB,EAC9BC,QAAQC,YAAY,EACpBC,WAAWC,IAAI,EACfC,YAAY,EACb,GAAGC,KAAKC,KAAK,CAAC,IAAI,CAACC,aAAa,CAACC,KAAK,CAAC;QAExC,oDAAoD;QACpD,IAAI,wBAAwBC,IAAI,CAACV,2BAA2B;YAC1D,MAAMW,MAAM,qBAEX,CAFW,IAAIC,MACd,GAAGC,IAAAA,gBAAI,EAAC,UAAU,gBAAgB,EAAEC,IAAAA,gBAAI,EAAC,sBAAsB,CAAC,CAAC,GADvD,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAH,IAAII,IAAI,GAAG;YACXlB,SAASc;YACT;QACF;QAEA,MAAM,EACJK,KAAK,EACLC,QAAQ,EACRC,WAAW,EACXC,cAAc,EACdC,SAASC,UAAU,EACpB,GAAG,IAAI,CAACC,UAAU;QAEnB,IAAIJ,eAAe,CAAC,kBAAkBR,IAAI,CAACQ,cAAc;YACvD,MAAMP,MAAM,qBAEX,CAFW,IAAIC,MACd,2FADU,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAD,IAAII,IAAI,GAAG;YACXlB,SAASc;YACT;QACF;QAEA;;;;;;;;;KASC,GACD,MAAMY,eAAe,CACnBC,SACAC,KACAC,SACAC;YAEA,MAAMC,OAAO;gBAAEC,SAAS,IAAI,CAACC,WAAW;gBAAEN;YAAQ;YAClD,MAAMO,mBAAmBC,qBAAW,CAACC,eAAe,CAClD,IAAI,EACJ,CAAC,mBAAmB,EAAEN,oBAAoB,OAAO,KAC/CD,UAAU,OAAO,GAClB,CAAC,EAAED,KAAK,EACTG;YAEF,MAAMM,aAAa,GAAGhB,YAAY,OAAO,EAAEa,kBAAkB;YAC7D,sCAAsC;YACtC,IAAI,CAACd,UAAU;gBACb,IAAI,CAACkB,QAAQ,CAACJ,kBAAkBP,SAAS;YAC3C;YACA,6DAA6D;YAC7D,OAAOU;QACT;QAEA,IAAI;YACF,kFAAkF;YAClF,8FAA8F;YAC9F,MAAME,aAAyBC,QAAQlB,gBAAgBmB,OAAO;YAC9D,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GACrE,MAAMnD,mBAAmBE,UAAU,CAAC,eAAeC,YAAY,CAAC,IAC9DwC,WAAW;oBACTlC;oBACAG;oBACAD;oBACAmB;oBACAsB,SAAS,CAACC,MACRC,IAAAA,eAAS,EAAC,IAAI,CAACF,OAAO,EACpB9C,aAAI,CAACiD,OAAO,CACVjD,aAAI,CAACkD,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE9B,4BAE9B8C,IAAII,UAAU,CAAC,OAAOJ,MAAM,CAAC,EAAE,EAAEA,KAAK;oBAE1C9B;oBACAC;oBACAkC,eAAe,IAAI;gBACrB;YAGJ,MAAM,EAAE/B,OAAO,EAAE,GAAG,MAAMC;YAE1B,gFAAgF;YAChF,MAAM+B,WAAuC,EAAE;YAE/C,oEAAoE;YACpE,MAAMC,iBAAiBrB,qBAAW,CAACsB,aAAa,CAC9CC,OAAOC,IAAI,CAACjB,MACZ,QACA,OACA;YAGF,6FAA6F;YAC7F,MAAMkB,SAAS,MAAMhE,mBAClBE,UAAU,CAAC,WACXC,YAAY,CAAC,IACZwB,QACEsC,IAAAA,wBAAqB,EAAC;oBACpBN,SAAAA;oBACAZ;oBACAE;oBACAC;oBACAF;oBACAG;gBACF,IACAe,OAAO,CAACpB,KAAK;oBACbiB,MAAMI;gBACR;YAGJ,MAAMC,MAAM;gBACVC,MAAM;gBACNC,SAASN,OAAOO,SAAS,CAACD,OAAO;gBACjCE,MAAMR,OAAOQ,IAAI;YACnB;YAEA,uHAAuH;YACvHpE,SAAS,MAAM4D,OAAOlB,GAAG,EAAE,MAAM;gBAC/Ba,SAAAA;gBACAS;gBACAR;YACF;QACF,EAAE,OAAO1C,KAAU;YACjBd,SAASc;QACX;IACF;AACF", "ignoreList": [0]}