{"version": 3, "sources": ["../../../../src/build/segment-config/app/app-segments.ts"], "sourcesContent": ["import type { LoadComponentsReturnType } from '../../../server/load-components'\nimport type { Params } from '../../../server/request/params'\nimport type {\n  AppPageRouteModule,\n  AppPageModule,\n} from '../../../server/route-modules/app-page/module.compiled'\nimport type {\n  AppRouteRouteModule,\n  AppRouteModule,\n} from '../../../server/route-modules/app-route/module.compiled'\nimport {\n  type AppSegmentConfig,\n  parseAppSegmentConfig,\n} from './app-segment-config'\n\nimport { InvariantError } from '../../../shared/lib/invariant-error'\nimport {\n  isAppRouteRouteModule,\n  isAppPageRouteModule,\n} from '../../../server/route-modules/checks'\nimport { isClientReference } from '../../../lib/client-and-server-references'\nimport { getSegmentParam } from '../../../server/app-render/get-segment-param'\nimport {\n  getLayoutOrPageModule,\n  type LoaderTree,\n} from '../../../server/lib/app-dir-module'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\ntype GenerateStaticParams = (options: { params?: Params }) => Promise<Params[]>\n\n/**\n * Parses the app config and attaches it to the segment.\n */\nfunction attach(segment: AppSegment, userland: unknown, route: string) {\n  // If the userland is not an object, then we can't do anything with it.\n  if (typeof userland !== 'object' || userland === null) {\n    return\n  }\n\n  // Try to parse the application configuration.\n  const config = parseAppSegmentConfig(userland, route)\n\n  // If there was any keys on the config, then attach it to the segment.\n  if (Object.keys(config).length > 0) {\n    segment.config = config\n  }\n\n  if (\n    'generateStaticParams' in userland &&\n    typeof userland.generateStaticParams === 'function'\n  ) {\n    segment.generateStaticParams =\n      userland.generateStaticParams as GenerateStaticParams\n\n    // Validate that `generateStaticParams` makes sense in this context.\n    if (segment.config?.runtime === 'edge') {\n      throw new Error(\n        'Edge runtime is not supported with `generateStaticParams`.'\n      )\n    }\n  }\n}\n\nexport type AppSegment = {\n  name: string\n  param: string | undefined\n  filePath: string | undefined\n  config: AppSegmentConfig | undefined\n  isDynamicSegment: boolean\n  generateStaticParams: GenerateStaticParams | undefined\n}\n\n/**\n * Walks the loader tree and collects the generate parameters for each segment.\n *\n * @param routeModule the app page route module\n * @returns the segments for the app page route module\n */\nasync function collectAppPageSegments(routeModule: AppPageRouteModule) {\n  // We keep track of unique segments, since with parallel routes, it's possible\n  // to see the same segment multiple times.\n  const uniqueSegments = new Map<string, AppSegment>()\n\n  // Queue will store tuples of [loaderTree, currentSegments]\n  type QueueItem = [LoaderTree, AppSegment[]]\n  const queue: QueueItem[] = [[routeModule.userland.loaderTree, []]]\n\n  while (queue.length > 0) {\n    const [loaderTree, currentSegments] = queue.shift()!\n    const [name, parallelRoutes] = loaderTree\n\n    // Process current node\n    const { mod: userland, filePath } = await getLayoutOrPageModule(loaderTree)\n    const isClientComponent = userland && isClientReference(userland)\n\n    const param = getSegmentParam(name)?.param\n\n    const segment: AppSegment = {\n      name,\n      param,\n      filePath,\n      config: undefined,\n      isDynamicSegment: !!param,\n      generateStaticParams: undefined,\n    }\n\n    // Only server components can have app segment configurations\n    if (!isClientComponent) {\n      attach(segment, userland, routeModule.definition.pathname)\n    }\n\n    // Create a unique key for the segment\n    const segmentKey = getSegmentKey(segment)\n    if (!uniqueSegments.has(segmentKey)) {\n      uniqueSegments.set(segmentKey, segment)\n    }\n\n    const updatedSegments = [...currentSegments, segment]\n\n    // If this is a page segment, we've reached a leaf node\n    if (name === PAGE_SEGMENT_KEY) {\n      // Add all segments in the current path\n      updatedSegments.forEach((seg) => {\n        const key = getSegmentKey(seg)\n        uniqueSegments.set(key, seg)\n      })\n    }\n\n    // Add all parallel routes to the queue\n    for (const parallelRouteKey in parallelRoutes) {\n      const parallelRoute = parallelRoutes[parallelRouteKey]\n      queue.push([parallelRoute, updatedSegments])\n    }\n  }\n\n  return Array.from(uniqueSegments.values())\n}\n\nfunction getSegmentKey(segment: AppSegment) {\n  return `${segment.name}-${segment.filePath ?? ''}-${segment.param ?? ''}`\n}\n\n/**\n * Collects the segments for a given app route module.\n *\n * @param routeModule the app route module\n * @returns the segments for the app route module\n */\nfunction collectAppRouteSegments(\n  routeModule: AppRouteRouteModule\n): AppSegment[] {\n  // Get the pathname parts, slice off the first element (which is empty).\n  const parts = routeModule.definition.pathname.split('/').slice(1)\n  if (parts.length === 0) {\n    throw new InvariantError('Expected at least one segment')\n  }\n\n  // Generate all the segments.\n  const segments: AppSegment[] = parts.map((name) => {\n    const param = getSegmentParam(name)?.param\n\n    return {\n      name,\n      param,\n      filePath: undefined,\n      isDynamicSegment: !!param,\n      config: undefined,\n      generateStaticParams: undefined,\n    }\n  })\n\n  // We know we have at least one, we verified this above. We should get the\n  // last segment which represents the root route module.\n  const segment = segments[segments.length - 1]\n\n  segment.filePath = routeModule.definition.filename\n\n  // Extract the segment config from the userland module.\n  attach(segment, routeModule.userland, routeModule.definition.pathname)\n\n  return segments\n}\n\n/**\n * Collects the segments for a given route module.\n *\n * @param components the loaded components\n * @returns the segments for the route module\n */\nexport function collectSegments({\n  routeModule,\n}: LoadComponentsReturnType<AppPageModule | AppRouteModule>):\n  | Promise<AppSegment[]>\n  | AppSegment[] {\n  if (isAppRouteRouteModule(routeModule)) {\n    return collectAppRouteSegments(routeModule)\n  }\n\n  if (isAppPageRouteModule(routeModule)) {\n    return collectAppPageSegments(routeModule)\n  }\n\n  throw new InvariantError(\n    'Expected a route module to be one of app route or page'\n  )\n}\n"], "names": ["collectSegments", "attach", "segment", "userland", "route", "config", "parseAppSegmentConfig", "Object", "keys", "length", "generateStaticParams", "runtime", "Error", "collectAppPageSegments", "routeModule", "uniqueSegments", "Map", "queue", "loaderTree", "getSegmentParam", "currentSegments", "shift", "name", "parallelRoutes", "mod", "filePath", "getLayoutOrPageModule", "isClientComponent", "isClientReference", "param", "undefined", "isDynamicSegment", "definition", "pathname", "segmentKey", "getSegmentKey", "has", "set", "updatedSegments", "PAGE_SEGMENT_KEY", "for<PERSON>ach", "seg", "key", "parallelRouteKey", "parallelRoute", "push", "Array", "from", "values", "collectAppRouteSegments", "parts", "split", "slice", "InvariantError", "segments", "map", "filename", "isAppRouteRouteModule", "isAppPageRouteModule"], "mappings": ";;;;+BA6LgBA;;;eAAAA;;;kCAhLT;gCAEwB;wBAIxB;2CAC2B;iCACF;8BAIzB;yBAC0B;AAIjC;;CAEC,GACD,SAASC,OAAOC,OAAmB,EAAEC,QAAiB,EAAEC,KAAa;IACnE,uEAAuE;IACvE,IAAI,OAAOD,aAAa,YAAYA,aAAa,MAAM;QACrD;IACF;IAEA,8CAA8C;IAC9C,MAAME,SAASC,IAAAA,uCAAqB,EAACH,UAAUC;IAE/C,sEAAsE;IACtE,IAAIG,OAAOC,IAAI,CAACH,QAAQI,MAAM,GAAG,GAAG;QAClCP,QAAQG,MAAM,GAAGA;IACnB;IAEA,IACE,0BAA0BF,YAC1B,OAAOA,SAASO,oBAAoB,KAAK,YACzC;YAKIR;QAJJA,QAAQQ,oBAAoB,GAC1BP,SAASO,oBAAoB;QAE/B,oEAAoE;QACpE,IAAIR,EAAAA,kBAAAA,QAAQG,MAAM,qBAAdH,gBAAgBS,OAAO,MAAK,QAAQ;YACtC,MAAM,qBAEL,CAFK,IAAIC,MACR,+DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF;AAWA;;;;;CAKC,GACD,eAAeC,uBAAuBC,WAA+B;IACnE,8EAA8E;IAC9E,0CAA0C;IAC1C,MAAMC,iBAAiB,IAAIC;IAI3B,MAAMC,QAAqB;QAAC;YAACH,YAAYX,QAAQ,CAACe,UAAU;YAAE,EAAE;SAAC;KAAC;IAElE,MAAOD,MAAMR,MAAM,GAAG,EAAG;YAQTU;QAPd,MAAM,CAACD,YAAYE,gBAAgB,GAAGH,MAAMI,KAAK;QACjD,MAAM,CAACC,MAAMC,eAAe,GAAGL;QAE/B,uBAAuB;QACvB,MAAM,EAAEM,KAAKrB,QAAQ,EAAEsB,QAAQ,EAAE,GAAG,MAAMC,IAAAA,mCAAqB,EAACR;QAChE,MAAMS,oBAAoBxB,YAAYyB,IAAAA,4CAAiB,EAACzB;QAExD,MAAM0B,SAAQV,mBAAAA,IAAAA,gCAAe,EAACG,0BAAhBH,iBAAuBU,KAAK;QAE1C,MAAM3B,UAAsB;YAC1BoB;YACAO;YACAJ;YACApB,QAAQyB;YACRC,kBAAkB,CAAC,CAACF;YACpBnB,sBAAsBoB;QACxB;QAEA,6DAA6D;QAC7D,IAAI,CAACH,mBAAmB;YACtB1B,OAAOC,SAASC,UAAUW,YAAYkB,UAAU,CAACC,QAAQ;QAC3D;QAEA,sCAAsC;QACtC,MAAMC,aAAaC,cAAcjC;QACjC,IAAI,CAACa,eAAeqB,GAAG,CAACF,aAAa;YACnCnB,eAAesB,GAAG,CAACH,YAAYhC;QACjC;QAEA,MAAMoC,kBAAkB;eAAIlB;YAAiBlB;SAAQ;QAErD,uDAAuD;QACvD,IAAIoB,SAASiB,yBAAgB,EAAE;YAC7B,uCAAuC;YACvCD,gBAAgBE,OAAO,CAAC,CAACC;gBACvB,MAAMC,MAAMP,cAAcM;gBAC1B1B,eAAesB,GAAG,CAACK,KAAKD;YAC1B;QACF;QAEA,uCAAuC;QACvC,IAAK,MAAME,oBAAoBpB,eAAgB;YAC7C,MAAMqB,gBAAgBrB,cAAc,CAACoB,iBAAiB;YACtD1B,MAAM4B,IAAI,CAAC;gBAACD;gBAAeN;aAAgB;QAC7C;IACF;IAEA,OAAOQ,MAAMC,IAAI,CAAChC,eAAeiC,MAAM;AACzC;AAEA,SAASb,cAAcjC,OAAmB;IACxC,OAAO,GAAGA,QAAQoB,IAAI,CAAC,CAAC,EAAEpB,QAAQuB,QAAQ,IAAI,GAAG,CAAC,EAAEvB,QAAQ2B,KAAK,IAAI,IAAI;AAC3E;AAEA;;;;;CAKC,GACD,SAASoB,wBACPnC,WAAgC;IAEhC,wEAAwE;IACxE,MAAMoC,QAAQpC,YAAYkB,UAAU,CAACC,QAAQ,CAACkB,KAAK,CAAC,KAAKC,KAAK,CAAC;IAC/D,IAAIF,MAAMzC,MAAM,KAAK,GAAG;QACtB,MAAM,qBAAmD,CAAnD,IAAI4C,8BAAc,CAAC,kCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,6BAA6B;IAC7B,MAAMC,WAAyBJ,MAAMK,GAAG,CAAC,CAACjC;YAC1BH;QAAd,MAAMU,SAAQV,mBAAAA,IAAAA,gCAAe,EAACG,0BAAhBH,iBAAuBU,KAAK;QAE1C,OAAO;YACLP;YACAO;YACAJ,UAAUK;YACVC,kBAAkB,CAAC,CAACF;YACpBxB,QAAQyB;YACRpB,sBAAsBoB;QACxB;IACF;IAEA,0EAA0E;IAC1E,uDAAuD;IACvD,MAAM5B,UAAUoD,QAAQ,CAACA,SAAS7C,MAAM,GAAG,EAAE;IAE7CP,QAAQuB,QAAQ,GAAGX,YAAYkB,UAAU,CAACwB,QAAQ;IAElD,uDAAuD;IACvDvD,OAAOC,SAASY,YAAYX,QAAQ,EAAEW,YAAYkB,UAAU,CAACC,QAAQ;IAErE,OAAOqB;AACT;AAQO,SAAStD,gBAAgB,EAC9Bc,WAAW,EAC8C;IAGzD,IAAI2C,IAAAA,6BAAqB,EAAC3C,cAAc;QACtC,OAAOmC,wBAAwBnC;IACjC;IAEA,IAAI4C,IAAAA,4BAAoB,EAAC5C,cAAc;QACrC,OAAOD,uBAAuBC;IAChC;IAEA,MAAM,qBAEL,CAFK,IAAIuC,8BAAc,CACtB,2DADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF", "ignoreList": [0]}