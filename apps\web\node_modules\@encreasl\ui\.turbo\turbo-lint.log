[?9001h[?1004h[?25l[2J[m[H]0;C:\WINDOWS\system32\cmd.exe [?25h[?25l
> @encreasl/ui@0.1.0 lint C:\Users\<USER>\Desktop\encreasl\packages\ui
> eslint . --max-warnings 0[5;1H[?25h[?25l
Oops! Something went wrong! :([8;1HESLint: 9.31.0[10;1HESLint couldn't find an eslint.config.(js|mjs|cjs) file.[12;1HFrom ESLint v9.0.0, the default configuration file is now eslint.config.js.
If you are using a .eslintrc.* file, please follow the migration guide
to update your configuration file to the new format:[16;1Hhttps://eslint.org/docs/latest/use/configure/migration-guide[18;1HIf you still have problems after following the migration guide, please stop by
https://eslint.org/chat/help to chat with the team.[21;1H[?25h[30m[41m ELIFECYCLE [m [31mCommand failed with exit code 2.
[m[30m[43m WARN [m  Local package.json exists, but node_modules missing, did you mean to install?
[?9001l[?1004l
