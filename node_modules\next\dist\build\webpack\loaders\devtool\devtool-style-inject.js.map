{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/devtool/devtool-style-inject.js"], "sourcesContent": ["/* @ts-check */\n/**\n * Style injection mechanism for Next.js devtools with shadow DOM support\n * Handles caching of style elements when the nextjs-portal shadow root is not available\n */\n\n// Global cache for style elements when shadow root is not available\nif (typeof window !== 'undefined') {\n  window._nextjsDevtoolsStyleCache = window._nextjsDevtoolsStyleCache || {\n    pendingElements: [],\n    isObserving: false,\n    lastInsertedElement: null,\n    cachedShadowRoot: null, // Cache the shadow root once found\n  }\n}\n\n/**\n * @returns {ShadowRoot | null}\n */\nfunction getShadowRoot() {\n  const cache = window._nextjsDevtoolsStyleCache\n\n  // Return cached shadow root if available\n  if (cache.cachedShadowRoot) {\n    return cache.cachedShadowRoot\n  }\n\n  // Query the DOM and cache the result if found\n  const portal = document.querySelector('nextjs-portal')\n  const shadowRoot = portal?.shadowRoot || null\n\n  if (shadowRoot) {\n    cache.cachedShadowRoot = shadowRoot\n  }\n\n  return shadowRoot\n}\n\n/**\n * @param {HTMLElement} element\n * @param {ShadowRoot} shadowRoot\n */\nfunction insertElementIntoShadowRoot(element, shadowRoot) {\n  const cache = window._nextjsDevtoolsStyleCache\n\n  if (!cache.lastInsertedElement) {\n    shadowRoot.insertBefore(element, shadowRoot.firstChild)\n  } else if (cache.lastInsertedElement.nextSibling) {\n    shadowRoot.insertBefore(element, cache.lastInsertedElement.nextSibling)\n  } else {\n    shadowRoot.appendChild(element)\n  }\n\n  cache.lastInsertedElement = element\n}\n\nfunction flushCachedElements() {\n  const cache = window._nextjsDevtoolsStyleCache\n  const shadowRoot = getShadowRoot()\n\n  if (!shadowRoot) {\n    return\n  }\n\n  cache.pendingElements.forEach((element) => {\n    insertElementIntoShadowRoot(element, shadowRoot)\n  })\n  cache.pendingElements = []\n}\n\nfunction startObservingForPortal() {\n  const cache = window._nextjsDevtoolsStyleCache\n\n  if (cache.isObserving) {\n    return\n  }\n  cache.isObserving = true\n\n  // First check if the portal already exists\n  const shadowRoot = getShadowRoot() // This will cache it if found\n  if (shadowRoot) {\n    flushCachedElements()\n    return\n  }\n\n  // Set up MutationObserver to watch for the portal element\n  const observer = new MutationObserver((mutations) => {\n    if (mutations.length === 0 || mutations[0].addedNodes.length === 0) {\n      return\n    }\n\n    // Check if mutation is script[data-nextjs-dev-overlay] tag, which is the\n    // parent of the nextjs-portal element\n    const mutationNode = mutations[0].addedNodes[0]\n    let portalNode = null\n    if (\n      // app router: body > script[data-nextjs-dev-overlay] > nextjs-portal\n      mutationNode.tagName === 'SCRIPT' &&\n      mutationNode.getAttribute('data-nextjs-dev-overlay')\n    ) {\n      portalNode = mutationNode.firstChild\n    } else if (\n      // pages router: body > nextjs-portal\n      mutationNode.tagName === 'NEXTJS-PORTAL'\n    ) {\n      portalNode = mutationNode\n    }\n    if (!portalNode) {\n      return\n    }\n\n    // Wait until shadow root is available\n    const checkShadowRoot = () => {\n      if (getShadowRoot()) {\n        flushCachedElements()\n        observer.disconnect()\n        cache.isObserving = false\n      } else {\n        // Try again after a short delay\n        setTimeout(checkShadowRoot, 20)\n      }\n    }\n    checkShadowRoot()\n  })\n\n  observer.observe(document.body, {\n    childList: true,\n    subtree: true,\n  })\n}\n\n/**\n * @param {HTMLElement} element\n */\nfunction insertAtTop(element) {\n  // Add special recognizable data prop to element\n  element.setAttribute('data-nextjs-dev-tool-style', 'true')\n\n  const shadowRoot = getShadowRoot()\n  if (shadowRoot) {\n    // Shadow root is available, insert directly\n    insertElementIntoShadowRoot(element, shadowRoot)\n  } else {\n    // Shadow root not available, cache the element\n    const cache = window._nextjsDevtoolsStyleCache\n    cache.pendingElements.push(element)\n\n    // Start observing for the portal if not already observing\n    startObservingForPortal()\n  }\n}\n\nmodule.exports = insertAtTop\n"], "names": ["window", "_nextjsDevtoolsStyleCache", "pendingElements", "isObserving", "lastInsertedElement", "cachedShadowRoot", "getShadowRoot", "cache", "portal", "document", "querySelector", "shadowRoot", "insertElementIntoShadowRoot", "element", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "append<PERSON><PERSON><PERSON>", "flushCachedElements", "for<PERSON>ach", "startObservingForPortal", "observer", "MutationObserver", "mutations", "length", "addedNodes", "mutationNode", "portalNode", "tagName", "getAttribute", "checkShadowRoot", "disconnect", "setTimeout", "observe", "body", "childList", "subtree", "insertAtTop", "setAttribute", "push", "module", "exports"], "mappings": "AAAA,aAAa,GACb;;;CAGC,GAED,oEAAoE;;AACpE,IAAI,OAAOA,WAAW,aAAa;IACjCA,OAAOC,yBAAyB,GAAGD,OAAOC,yBAAyB,IAAI;QACrEC,iBAAiB,EAAE;QACnBC,aAAa;QACbC,qBAAqB;QACrBC,kBAAkB;IACpB;AACF;AAEA;;CAEC,GACD,SAASC;IACP,MAAMC,QAAQP,OAAOC,yBAAyB;IAE9C,yCAAyC;IACzC,IAAIM,MAAMF,gBAAgB,EAAE;QAC1B,OAAOE,MAAMF,gBAAgB;IAC/B;IAEA,8CAA8C;IAC9C,MAAMG,SAASC,SAASC,aAAa,CAAC;IACtC,MAAMC,aAAaH,CAAAA,0BAAAA,OAAQG,UAAU,KAAI;IAEzC,IAAIA,YAAY;QACdJ,MAAMF,gBAAgB,GAAGM;IAC3B;IAEA,OAAOA;AACT;AAEA;;;CAGC,GACD,SAASC,4BAA4BC,OAAO,EAAEF,UAAU;IACtD,MAAMJ,QAAQP,OAAOC,yBAAyB;IAE9C,IAAI,CAACM,MAAMH,mBAAmB,EAAE;QAC9BO,WAAWG,YAAY,CAACD,SAASF,WAAWI,UAAU;IACxD,OAAO,IAAIR,MAAMH,mBAAmB,CAACY,WAAW,EAAE;QAChDL,WAAWG,YAAY,CAACD,SAASN,MAAMH,mBAAmB,CAACY,WAAW;IACxE,OAAO;QACLL,WAAWM,WAAW,CAACJ;IACzB;IAEAN,MAAMH,mBAAmB,GAAGS;AAC9B;AAEA,SAASK;IACP,MAAMX,QAAQP,OAAOC,yBAAyB;IAC9C,MAAMU,aAAaL;IAEnB,IAAI,CAACK,YAAY;QACf;IACF;IAEAJ,MAAML,eAAe,CAACiB,OAAO,CAAC,CAACN;QAC7BD,4BAA4BC,SAASF;IACvC;IACAJ,MAAML,eAAe,GAAG,EAAE;AAC5B;AAEA,SAASkB;IACP,MAAMb,QAAQP,OAAOC,yBAAyB;IAE9C,IAAIM,MAAMJ,WAAW,EAAE;QACrB;IACF;IACAI,MAAMJ,WAAW,GAAG;IAEpB,2CAA2C;IAC3C,MAAMQ,aAAaL,gBAAgB,8BAA8B;;IACjE,IAAIK,YAAY;QACdO;QACA;IACF;IAEA,0DAA0D;IAC1D,MAAMG,WAAW,IAAIC,iBAAiB,CAACC;QACrC,IAAIA,UAAUC,MAAM,KAAK,KAAKD,SAAS,CAAC,EAAE,CAACE,UAAU,CAACD,MAAM,KAAK,GAAG;YAClE;QACF;QAEA,yEAAyE;QACzE,sCAAsC;QACtC,MAAME,eAAeH,SAAS,CAAC,EAAE,CAACE,UAAU,CAAC,EAAE;QAC/C,IAAIE,aAAa;QACjB,IACE,qEAAqE;QACrED,aAAaE,OAAO,KAAK,YACzBF,aAAaG,YAAY,CAAC,4BAC1B;YACAF,aAAaD,aAAaX,UAAU;QACtC,OAAO,IACL,qCAAqC;QACrCW,aAAaE,OAAO,KAAK,iBACzB;YACAD,aAAaD;QACf;QACA,IAAI,CAACC,YAAY;YACf;QACF;QAEA,sCAAsC;QACtC,MAAMG,kBAAkB;YACtB,IAAIxB,iBAAiB;gBACnBY;gBACAG,SAASU,UAAU;gBACnBxB,MAAMJ,WAAW,GAAG;YACtB,OAAO;gBACL,gCAAgC;gBAChC6B,WAAWF,iBAAiB;YAC9B;QACF;QACAA;IACF;IAEAT,SAASY,OAAO,CAACxB,SAASyB,IAAI,EAAE;QAC9BC,WAAW;QACXC,SAAS;IACX;AACF;AAEA;;CAEC,GACD,SAASC,YAAYxB,OAAO;IAC1B,gDAAgD;IAChDA,QAAQyB,YAAY,CAAC,8BAA8B;IAEnD,MAAM3B,aAAaL;IACnB,IAAIK,YAAY;QACd,4CAA4C;QAC5CC,4BAA4BC,SAASF;IACvC,OAAO;QACL,+CAA+C;QAC/C,MAAMJ,QAAQP,OAAOC,yBAAyB;QAC9CM,MAAML,eAAe,CAACqC,IAAI,CAAC1B;QAE3B,0DAA0D;QAC1DO;IACF;AACF;AAEAoB,OAAOC,OAAO,GAAGJ", "ignoreList": [0]}