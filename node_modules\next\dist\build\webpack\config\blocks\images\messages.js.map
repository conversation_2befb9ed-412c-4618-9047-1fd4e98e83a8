{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/images/messages.ts"], "sourcesContent": ["import { bold, cyan } from '../../../../../lib/picocolors'\n\nexport function getCustomDocumentImageError() {\n  return `Images ${bold('cannot')} be imported within ${cyan(\n    'pages/_document.js'\n  )}. Please move image imports that need to be displayed on every page into ${cyan(\n    'pages/_app.js'\n  )}.\\nRead more: https://nextjs.org/docs/messages/custom-document-image-import`\n}\n"], "names": ["getCustomDocumentImageError", "bold", "cyan"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;4BAFW;AAEpB,SAASA;IACd,OAAO,CAAC,OAAO,EAAEC,IAAAA,gBAAI,EAAC,UAAU,oBAAoB,EAAEC,IAAAA,gBAAI,EACxD,sBACA,yEAAyE,EAAEA,IAAAA,gBAAI,EAC/E,iBACA,2EAA2E,CAAC;AAChF", "ignoreList": [0]}