{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/encreasl/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-blue-600\">encreasl</h1>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-4\">\n                <a href=\"#services\" className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\">Services</a>\n                <a href=\"#about\" className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\">About</a>\n                <a href=\"#contact\" className=\"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\">Contact</a>\n                <button className=\"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors\">\n                  Get Started\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Scale Your <span className=\"text-blue-600\">Ecommerce</span> Business\n            </h1>\n            <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n              Transform your online store with data-driven marketing strategies. We help ecommerce businesses increase sales, optimize conversions, and scale profitably.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors\">\n                Start Growing Today\n              </button>\n              <button className=\"border border-gray-300 text-gray-700 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-50 transition-colors\">\n                View Case Studies\n              </button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Section */}\n      <section id=\"services\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Our Services\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              Comprehensive ecommerce marketing solutions to drive growth and maximize ROI\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-xl\">\n              <div className=\"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-6\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Performance Marketing</h3>\n              <p className=\"text-gray-600\">\n                Drive qualified traffic and maximize conversions with targeted PPC campaigns, social media advertising, and SEO optimization.\n              </p>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 p-8 rounded-xl\">\n              <div className=\"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-6\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Conversion Optimization</h3>\n              <p className=\"text-gray-600\">\n                Increase your conversion rates with A/B testing, user experience optimization, and data-driven website improvements.\n              </p>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-xl\">\n              <div className=\"w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-6\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">Email & Retention Marketing</h3>\n              <p className=\"text-gray-600\">\n                Build customer loyalty and increase lifetime value with strategic email campaigns and retention marketing programs.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-20 bg-blue-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">500+</div>\n              <div className=\"text-blue-100\">Clients Served</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">$50M+</div>\n              <div className=\"text-blue-100\">Revenue Generated</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">3.2x</div>\n              <div className=\"text-blue-100\">Average ROI</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-white mb-2\">98%</div>\n              <div className=\"text-blue-100\">Client Satisfaction</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            Ready to Scale Your Ecommerce Business?\n          </h2>\n          <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n            Join hundreds of successful ecommerce brands that trust Encreasl to drive their growth.\n          </p>\n          <button className=\"bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors\">\n            Get Your Free Strategy Session\n          </button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <h3 className=\"text-2xl font-bold text-blue-600 mb-4\">encreasl</h3>\n              <p className=\"text-gray-600\">\n                Your trusted partner for ecommerce marketing success.\n              </p>\n            </div>\n            <div>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Services</h4>\n              <ul className=\"space-y-2 text-gray-600\">\n                <li>Performance Marketing</li>\n                <li>Conversion Optimization</li>\n                <li>Email Marketing</li>\n                <li>SEO & Content</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Company</h4>\n              <ul className=\"space-y-2 text-gray-600\">\n                <li>About Us</li>\n                <li>Case Studies</li>\n                <li>Blog</li>\n                <li>Careers</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Contact</h4>\n              <ul className=\"space-y-2 text-gray-600\">\n                <li><EMAIL></li>\n                <li>+1 (555) 123-4567</li>\n                <li>Schedule a Call</li>\n              </ul>\n            </div>\n          </div>\n          <div className=\"border-t mt-8 pt-8 text-center text-gray-600\">\n            <p>&copy; 2024 Encreasl. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAY,WAAU;sDAA+F;;;;;;sDAC7H,8OAAC;4CAAE,MAAK;4CAAS,WAAU;sDAA+F;;;;;;sDAC1H,8OAAC;4CAAE,MAAK;4CAAW,WAAU;sDAA+F;;;;;;sDAC5H,8OAAC;4CAAO,WAAU;sDAAsG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlI,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;kDACrD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAgB;;;;;;;0CAE7D,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAG5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAwG;;;;;;kDAG1H,8OAAC;wCAAO,WAAU;kDAAqH;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/I,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC5E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAO,WAAU;sCAAwG;;;;;;;;;;;;;;;;;0BAO9H,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAGR,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;8DACJ,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAIV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}