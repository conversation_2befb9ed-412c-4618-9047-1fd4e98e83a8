{"version": 3, "sources": ["../../../src/build/jest/jest.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport { resolve, join } from 'path'\nimport loadConfig from '../../server/config'\nimport { PHASE_TEST } from '../../shared/lib/constants'\nimport loadJsConfig from '../load-jsconfig'\nimport * as Log from '../output/log'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport { loadBindings, lockfilePatchPromise } from '../swc'\nimport type { JestTransformerConfig } from '../swc/jest-transformer'\nimport type { Config } from '@jest/types'\n\nconst DEFAULT_TRANSPILED_PACKAGES: string[] = require('../../lib/default-transpiled-packages.json')\n\nasync function getConfig(dir: string) {\n  const conf = await loadConfig(PHASE_TEST, dir)\n  return conf\n}\n\n/**\n * Loads closest package.json in the directory hierarchy\n */\nfunction loadClosestPackageJson(dir: string, attempts = 1): any {\n  if (attempts > 5) {\n    throw new Error(\"Can't resolve main package.json file\")\n  }\n  var mainPath = attempts === 1 ? './' : Array(attempts).join('../')\n  try {\n    return require(join(dir, mainPath + 'package.json'))\n  } catch (e) {\n    return loadClosestPackageJson(dir, attempts + 1)\n  }\n}\n\n/** Loads dotenv files and sets environment variables based on next config. */\nfunction setUpEnv(dir: string) {\n  const dev = false\n  loadEnvConfig(dir, dev, Log)\n}\n\n/**\n * @example\n * ```ts\n * // Usage in jest.config.js\n * const nextJest = require('next/jest');\n *\n * // Optionally provide path to Next.js app which will enable loading next.config.js and .env files\n * const createJestConfig = nextJest({ dir })\n *\n * // Any custom config you want to pass to Jest\n * const customJestConfig = {\n *     setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],\n * }\n *\n * // createJestConfig is exported in this way to ensure that next/jest can load the Next.js config which is async\n * module.exports = createJestConfig(customJestConfig)\n * ```\n *\n * Read more: [Next.js Docs: Setting up Jest with Next.js](https://nextjs.org/docs/app/building-your-application/testing/jest)\n */\nexport default function nextJest(options: { dir?: string } = {}) {\n  // createJestConfig\n  return (\n    customJestConfig?:\n      | Config.InitialProjectOptions\n      | (() => Promise<Config.InitialProjectOptions>)\n  ) => {\n    // Function that is provided as the module.exports of jest.config.js\n    // Will be called and awaited by Jest\n    return async (): Promise<Config.InitialProjectOptions> => {\n      let nextConfig\n      let jsConfig\n      let resolvedBaseUrl\n      let isEsmProject = false\n      let pagesDir: string | undefined\n      let serverComponents: boolean | undefined\n\n      if (options.dir) {\n        const resolvedDir = resolve(options.dir)\n        const packageConfig = loadClosestPackageJson(resolvedDir)\n        isEsmProject = packageConfig.type === 'module'\n\n        nextConfig = await getConfig(resolvedDir)\n        const findPagesDirResult = findPagesDir(resolvedDir)\n        serverComponents = !!findPagesDirResult.appDir\n        pagesDir = findPagesDirResult.pagesDir\n        setUpEnv(resolvedDir)\n        // TODO: revisit when bug in SWC is fixed that strips `.css`\n        const result = await loadJsConfig(resolvedDir, nextConfig)\n        jsConfig = result.jsConfig\n        resolvedBaseUrl = result.resolvedBaseUrl\n      }\n      // Ensure provided async config is supported\n      const resolvedJestConfig =\n        (typeof customJestConfig === 'function'\n          ? await customJestConfig()\n          : customJestConfig) ?? {}\n\n      // eagerly load swc bindings instead of waiting for transform calls\n      await loadBindings(nextConfig?.experimental?.useWasmBinary)\n\n      if (lockfilePatchPromise.cur) {\n        await lockfilePatchPromise.cur\n      }\n\n      const transpiled = (nextConfig?.transpilePackages ?? [])\n        .concat(DEFAULT_TRANSPILED_PACKAGES)\n        .join('|')\n\n      const jestTransformerConfig: JestTransformerConfig = {\n        modularizeImports: nextConfig?.modularizeImports,\n        swcPlugins: nextConfig?.experimental?.swcPlugins,\n        compilerOptions: nextConfig?.compiler,\n        jsConfig,\n        resolvedBaseUrl,\n        serverComponents,\n        isEsmProject,\n        pagesDir,\n      }\n      return {\n        ...resolvedJestConfig,\n\n        moduleNameMapper: {\n          // Handle CSS imports (with CSS modules)\n          // https://jestjs.io/docs/webpack#mocking-css-modules\n          '^.+\\\\.module\\\\.(css|sass|scss)$':\n            require.resolve('./object-proxy.js'),\n\n          // Handle CSS imports (without CSS modules)\n          '^.+\\\\.(css|sass|scss)$': require.resolve('./__mocks__/styleMock.js'),\n\n          // Handle image imports\n          '^.+\\\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$': require.resolve(\n            `./__mocks__/fileMock.js`\n          ),\n\n          // Keep .svg to it's own rule to make overriding easy\n          '^.+\\\\.(svg)$': require.resolve(`./__mocks__/fileMock.js`),\n\n          // Handle @next/font\n          '@next/font/(.*)': require.resolve('./__mocks__/nextFontMock.js'),\n          // Handle next/font\n          'next/font/(.*)': require.resolve('./__mocks__/nextFontMock.js'),\n          // Disable server-only\n          '^server-only$': require.resolve('./__mocks__/empty.js'),\n\n          // custom config comes last to ensure the above rules are matched,\n          // fixes the case where @pages/(.*) -> src/pages/$! doesn't break\n          // CSS/image mocks\n          ...(resolvedJestConfig.moduleNameMapper || {}),\n        },\n        testPathIgnorePatterns: [\n          // Don't look for tests in node_modules\n          '/node_modules/',\n          // Don't look for tests in the Next.js build output\n          '/.next/',\n          // Custom config can append to testPathIgnorePatterns but not modify it\n          // This is to ensure `.next` and `node_modules` are always excluded\n          ...(resolvedJestConfig.testPathIgnorePatterns || []),\n        ],\n\n        transform: {\n          // Use SWC to compile tests\n          '^.+\\\\.(js|jsx|ts|tsx|mjs)$': [\n            require.resolve('../swc/jest-transformer'),\n            jestTransformerConfig,\n          ],\n          // Allow for appending/overriding the default transforms\n          ...(resolvedJestConfig.transform || {}),\n        },\n\n        transformIgnorePatterns: [\n          // To match Next.js behavior node_modules is not transformed, only `transpiledPackages`\n          ...(transpiled\n            ? [\n                `/node_modules/(?!.pnpm)(?!(${transpiled})/)`,\n                `/node_modules/.pnpm/(?!(${transpiled.replace(\n                  /\\//g,\n                  '\\\\+'\n                )})@)`,\n              ]\n            : ['/node_modules/']),\n          // CSS modules are mocked so they don't need to be transformed\n          '^.+\\\\.module\\\\.(css|sass|scss)$',\n\n          // Custom config can append to transformIgnorePatterns but not modify it\n          // This is to ensure `node_modules` and .module.css/sass/scss are always excluded\n          ...(resolvedJestConfig.transformIgnorePatterns || []),\n        ],\n        watchPathIgnorePatterns: [\n          // Don't re-run tests when the Next.js build output changes\n          '/.next/',\n          ...(resolvedJestConfig.watchPathIgnorePatterns || []),\n        ],\n      }\n    }\n  }\n}\n"], "names": ["nextJest", "DEFAULT_TRANSPILED_PACKAGES", "require", "getConfig", "dir", "conf", "loadConfig", "PHASE_TEST", "loadClosestPackageJson", "attempts", "Error", "mainP<PERSON>", "Array", "join", "e", "setUpEnv", "dev", "loadEnvConfig", "Log", "options", "customJestConfig", "nextConfig", "jsConfig", "resolvedBaseUrl", "isEsmProject", "pagesDir", "serverComponents", "resolvedDir", "resolve", "packageConfig", "type", "findPagesDirResult", "findPagesDir", "appDir", "result", "loadJsConfig", "resolvedJestConfig", "loadBindings", "experimental", "useWasmBinary", "lockfilePatchPromise", "cur", "transpiled", "transpilePackages", "concat", "jestTransformerConfig", "modularizeImports", "swcPlugins", "compilerOptions", "compiler", "moduleNameMapper", "testPathIgnorePatterns", "transform", "transformIgnorePatterns", "replace", "watchPathIgnorePatterns"], "mappings": ";;;;+BAuCA;;;;;;;;;;;;;;;;;;;CAmBC,GACD;;;eAAwBA;;;qBA3DM;sBACA;+DACP;2BACI;qEACF;6DACJ;8BACQ;qBACsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAInD,MAAMC,8BAAwCC,QAAQ;AAEtD,eAAeC,UAAUC,GAAW;IAClC,MAAMC,OAAO,MAAMC,IAAAA,eAAU,EAACC,qBAAU,EAAEH;IAC1C,OAAOC;AACT;AAEA;;CAEC,GACD,SAASG,uBAAuBJ,GAAW,EAAEK,WAAW,CAAC;IACvD,IAAIA,WAAW,GAAG;QAChB,MAAM,qBAAiD,CAAjD,IAAIC,MAAM,yCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IACA,IAAIC,WAAWF,aAAa,IAAI,OAAOG,MAAMH,UAAUI,IAAI,CAAC;IAC5D,IAAI;QACF,OAAOX,QAAQW,IAAAA,UAAI,EAACT,KAAKO,WAAW;IACtC,EAAE,OAAOG,GAAG;QACV,OAAON,uBAAuBJ,KAAKK,WAAW;IAChD;AACF;AAEA,4EAA4E,GAC5E,SAASM,SAASX,GAAW;IAC3B,MAAMY,MAAM;IACZC,IAAAA,kBAAa,EAACb,KAAKY,KAAKE;AAC1B;AAsBe,SAASlB,SAASmB,UAA4B,CAAC,CAAC;IAC7D,mBAAmB;IACnB,OAAO,CACLC;QAIA,oEAAoE;QACpE,qCAAqC;QACrC,OAAO;gBA8BcC,0BAYLA;YAzCd,IAAIA;YACJ,IAAIC;YACJ,IAAIC;YACJ,IAAIC,eAAe;YACnB,IAAIC;YACJ,IAAIC;YAEJ,IAAIP,QAAQf,GAAG,EAAE;gBACf,MAAMuB,cAAcC,IAAAA,aAAO,EAACT,QAAQf,GAAG;gBACvC,MAAMyB,gBAAgBrB,uBAAuBmB;gBAC7CH,eAAeK,cAAcC,IAAI,KAAK;gBAEtCT,aAAa,MAAMlB,UAAUwB;gBAC7B,MAAMI,qBAAqBC,IAAAA,0BAAY,EAACL;gBACxCD,mBAAmB,CAAC,CAACK,mBAAmBE,MAAM;gBAC9CR,WAAWM,mBAAmBN,QAAQ;gBACtCV,SAASY;gBACT,4DAA4D;gBAC5D,MAAMO,SAAS,MAAMC,IAAAA,qBAAY,EAACR,aAAaN;gBAC/CC,WAAWY,OAAOZ,QAAQ;gBAC1BC,kBAAkBW,OAAOX,eAAe;YAC1C;YACA,4CAA4C;YAC5C,MAAMa,qBACJ,AAAC,CAAA,OAAOhB,qBAAqB,aACzB,MAAMA,qBACNA,gBAAe,KAAM,CAAC;YAE5B,mEAAmE;YACnE,MAAMiB,IAAAA,iBAAY,EAAChB,+BAAAA,2BAAAA,WAAYiB,YAAY,qBAAxBjB,yBAA0BkB,aAAa;YAE1D,IAAIC,yBAAoB,CAACC,GAAG,EAAE;gBAC5B,MAAMD,yBAAoB,CAACC,GAAG;YAChC;YAEA,MAAMC,aAAa,AAACrB,CAAAA,CAAAA,8BAAAA,WAAYsB,iBAAiB,KAAI,EAAE,AAAD,EACnDC,MAAM,CAAC3C,6BACPY,IAAI,CAAC;YAER,MAAMgC,wBAA+C;gBACnDC,iBAAiB,EAAEzB,8BAAAA,WAAYyB,iBAAiB;gBAChDC,UAAU,EAAE1B,+BAAAA,4BAAAA,WAAYiB,YAAY,qBAAxBjB,0BAA0B0B,UAAU;gBAChDC,eAAe,EAAE3B,8BAAAA,WAAY4B,QAAQ;gBACrC3B;gBACAC;gBACAG;gBACAF;gBACAC;YACF;YACA,OAAO;gBACL,GAAGW,kBAAkB;gBAErBc,kBAAkB;oBAChB,wCAAwC;oBACxC,qDAAqD;oBACrD,mCACEhD,QAAQ0B,OAAO,CAAC;oBAElB,2CAA2C;oBAC3C,0BAA0B1B,QAAQ0B,OAAO,CAAC;oBAE1C,uBAAuB;oBACvB,+CAA+C1B,QAAQ0B,OAAO,CAC5D,CAAC,uBAAuB,CAAC;oBAG3B,qDAAqD;oBACrD,gBAAgB1B,QAAQ0B,OAAO,CAAC,CAAC,uBAAuB,CAAC;oBAEzD,oBAAoB;oBACpB,mBAAmB1B,QAAQ0B,OAAO,CAAC;oBACnC,mBAAmB;oBACnB,kBAAkB1B,QAAQ0B,OAAO,CAAC;oBAClC,sBAAsB;oBACtB,iBAAiB1B,QAAQ0B,OAAO,CAAC;oBAEjC,kEAAkE;oBAClE,iEAAiE;oBACjE,kBAAkB;oBAClB,GAAIQ,mBAAmBc,gBAAgB,IAAI,CAAC,CAAC;gBAC/C;gBACAC,wBAAwB;oBACtB,uCAAuC;oBACvC;oBACA,mDAAmD;oBACnD;oBACA,uEAAuE;oBACvE,mEAAmE;uBAC/Df,mBAAmBe,sBAAsB,IAAI,EAAE;iBACpD;gBAEDC,WAAW;oBACT,2BAA2B;oBAC3B,8BAA8B;wBAC5BlD,QAAQ0B,OAAO,CAAC;wBAChBiB;qBACD;oBACD,wDAAwD;oBACxD,GAAIT,mBAAmBgB,SAAS,IAAI,CAAC,CAAC;gBACxC;gBAEAC,yBAAyB;oBACvB,uFAAuF;uBACnFX,aACA;wBACE,CAAC,2BAA2B,EAAEA,WAAW,GAAG,CAAC;wBAC7C,CAAC,wBAAwB,EAAEA,WAAWY,OAAO,CAC3C,OACA,OACA,GAAG,CAAC;qBACP,GACD;wBAAC;qBAAiB;oBACtB,8DAA8D;oBAC9D;oBAEA,wEAAwE;oBACxE,iFAAiF;uBAC7ElB,mBAAmBiB,uBAAuB,IAAI,EAAE;iBACrD;gBACDE,yBAAyB;oBACvB,2DAA2D;oBAC3D;uBACInB,mBAAmBmB,uBAAuB,IAAI,EAAE;iBACrD;YACH;QACF;IACF;AACF", "ignoreList": [0]}