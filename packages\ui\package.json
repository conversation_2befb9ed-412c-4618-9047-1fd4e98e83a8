{"name": "@encreasl/ui", "version": "0.1.0", "private": true, "exports": {"./button": "./src/button.tsx", "./card": "./src/card.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "type-check": "tsc --noEmit"}, "devDependencies": {"@encreasl/eslint-config": "workspace:*", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "typescript": "^5"}, "peerDependencies": {"react": "^19.1.0"}}